package com.yuedu.store.controller.business;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yuedu.store.query.CourseHoursLogNewQuery;
import com.yuedu.store.query.StoreCourseHoursPayQuery;
import com.yuedu.store.query.StoreRefundRecordQuery;
import com.yuedu.store.query.StudentQuery;
import com.yuedu.store.service.CourseExportService;
import com.yuedu.store.vo.CourseExportVO;
import com.yuedu.ydsf.common.core.util.R;
import com.yuedu.ydsf.common.security.annotation.StorePermission;
import com.yuedu.ydsf.common.security.util.StoreContextHolder;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;


/**
 * 导出控制器
 *
 * <AUTHOR>
 * @date 2025-06-13
 */
@Slf4j
@Validated
@RestController
@RequiredArgsConstructor
@RequestMapping("business/courseExport")
@Tag(description = "business/courseExport", name = "导出控制器")
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
@StorePermission

public class CourseExportController {

    private final CourseExportService courseExportService;

    /**
     * 课消导出明细
     *
     * @param courseHoursLogNewQuery 查询条件
     * @return excel 文件流
     */
    @Operation(summary = "课消导出明细", description = "课消导出明细")
    @PostMapping("/courseExport")
    public R courseExport(@RequestBody CourseHoursLogNewQuery courseHoursLogNewQuery) {
        courseHoursLogNewQuery.setStoreId(StoreContextHolder.getStoreId());
        courseExportService.getCourseExport(courseHoursLogNewQuery);
        return R.ok();
    }
    /**
     * 导出明细列表
     *
     * @param courseHoursLogNewQuery 查询条件
     * @return excel 文件流
     */
    @Operation(summary = "导出明细", description = "导出明细")
    @GetMapping("/courseExportList")
    public R<Page<CourseExportVO>> courseExportList(@ParameterObject Page page,@ParameterObject CourseHoursLogNewQuery courseHoursLogNewQuery) {
        courseHoursLogNewQuery.setStoreId(StoreContextHolder.getStoreId());
        return R.ok(courseExportService.getCourseExportList(page,courseHoursLogNewQuery));
    }

    /**
     * 模拟消费
     *
     */
    @Operation(summary = "模拟消费", description = "模拟消费")
    @GetMapping("/exportConsumer")
    public R exportConsumer(@RequestParam Long exporId) {
        courseExportService.courseExport(exporId);
//        courseExportService.studentExport(exporId);
//        courseExportService.courseHoursExport(exporId);
//        courseExportService.refundRecordExport(exporId);
        return R.ok();
    }

    /**
     * 学员报表导出明细
     *
     * @param studentQuery 查询条件
     * @return excel 文件流
     */
    @Operation(summary = "学员导出明细", description = "学员导出明细")
    @PostMapping("/studentExport")
    public R studentExport(@RequestBody StudentQuery studentQuery) {
        studentQuery.setStoreId(StoreContextHolder.getStoreId());
        courseExportService.getStudentExport(studentQuery);
        return R.ok();
    }

    /**
     * 收费单导出明细
     *
     * @param storeCourseHoursPayQuery 查询条件
     * @return excel 文件流
     */
    @Operation(summary = "收费单导出明细", description = "收费单导出明细")
    @PostMapping("/courseHoursExport")
    public R courseHoursExport(@RequestBody StoreCourseHoursPayQuery storeCourseHoursPayQuery) {
        storeCourseHoursPayQuery.setStoreId(StoreContextHolder.getStoreId());
        storeCourseHoursPayQuery.setSchoolId(StoreContextHolder.getSchoolId());
        courseExportService.getCourseHoursExport(storeCourseHoursPayQuery);
        return R.ok();
    }

    /**
     * 退费单导出明细
     *
     * @param storeRefundRecordQuery 查询条件
     * @return excel 文件流
     */
    @Operation(summary = "退费单导出明细", description = "退费单导出明细")
    @PostMapping("/refundRecordExport")
    public R courseHoursExport(@RequestBody StoreRefundRecordQuery storeRefundRecordQuery) {
        storeRefundRecordQuery.setStoreId(StoreContextHolder.getStoreId());
        storeRefundRecordQuery.setSchoolId(StoreContextHolder.getSchoolId());
        courseExportService.getRefundRecordExport(storeRefundRecordQuery);
        return R.ok();
    }

}
