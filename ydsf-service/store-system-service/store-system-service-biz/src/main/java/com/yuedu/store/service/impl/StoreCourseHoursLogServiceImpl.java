package com.yuedu.store.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yuedu.store.constant.enums.CourseHoursLogTypeEnum;
import com.yuedu.store.constant.enums.NullifyEnum;
import com.yuedu.store.dto.StoreCourseHoursLogDTO;
import com.yuedu.store.dto.StudentCourseHoursLogDTO;
import com.yuedu.store.entity.StoreCourseHoursLog;
import com.yuedu.store.entity.Student;
import com.yuedu.store.entity.ft.Member;
import com.yuedu.store.mapper.CourseHoursLogMapper;
import com.yuedu.store.mapper.StudentMapper;
import com.yuedu.store.mapper.ft.MemberMapper;
import com.yuedu.store.query.CourseHoursLogNewQuery;
import com.yuedu.store.query.CourseHoursLogQuery;
import com.yuedu.store.service.StoreCourseHoursLogService;
import com.yuedu.store.utils.DateUtils;
import com.yuedu.store.vo.CourseHoursLogNewVO;
import com.yuedu.store.vo.CourseHoursLogTotalVO;
import com.yuedu.store.vo.CourseHoursLogVO;
import com.yuedu.store.vo.StoreCourseHoursLogVO;
import com.yuedu.teaching.api.feign.RemoteCourseService;
import com.yuedu.teaching.api.feign.RemoteCourseTypeService;
import com.yuedu.teaching.api.feign.RemoteLessonPubService;
import com.yuedu.teaching.api.feign.RemoteStageService;
import com.yuedu.teaching.dto.CourseTypeDTO;
import com.yuedu.teaching.vo.CourseVO;
import com.yuedu.teaching.vo.LessonPubVO;
import com.yuedu.teaching.vo.StageVO;
import com.yuedu.ydsf.common.core.constant.enums.YesNoEnum;
import com.yuedu.ydsf.common.core.exception.BizException;
import com.yuedu.ydsf.common.core.util.R;
import com.yuedu.ydsf.eduConnect.api.constant.StageProductEnum;
import com.yuedu.ydsf.eduConnect.jw.api.dto.TimetableDTO;
import com.yuedu.ydsf.eduConnect.jw.api.feign.RemoteTimetableService;
import com.yuedu.ydsf.eduConnect.jw.api.vo.TimetableVO;
import jakarta.annotation.Resource;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.javers.common.collections.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 课时购买日志表
 *
 * <AUTHOR>
 * @date 2025-02-13 14:22:18
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class StoreCourseHoursLogServiceImpl extends ServiceImpl<CourseHoursLogMapper, StoreCourseHoursLog> implements StoreCourseHoursLogService {

    @Resource
    private final RemoteTimetableService remoteTimetableService;
    @Resource
    private StudentMapper studentMapper;
    @Resource
    private RemoteStageService remoteStageService;
    @Resource
    private RemoteCourseService remoteCourseService;
    @Resource
    private RemoteCourseTypeService remoteCourseTypeService;
    @Autowired
    private MemberMapper memberMapper;
    @Resource
    private CourseHoursLogMapper courseHoursLogMapper;
    @Resource
    private RemoteLessonPubService remoteLessonPubService;

    /**
     * 通过学生id查询课次记录
     *
     * @param page      分页参数
     * @param studentId 学生id
     * @return Page<CourseHoursLogVO>
     */
    @Override
    public Page<CourseHoursLogVO> getByStudentId(Page<StoreCourseHoursLog> page, Long studentId) {
        Page<StoreCourseHoursLog> pageRecord = this.page(page, Wrappers.lambdaQuery(StoreCourseHoursLog.class)
                .eq(StoreCourseHoursLog::getStudentId, studentId)
                .orderByDesc(StoreCourseHoursLog::getId));


        //把pageRecord里logType等于4获取5的，取出timetableId组成一个list
        List<Long> timetableIdList = pageRecord.getRecords().stream()
                .filter(item -> item.getLogType().equals(CourseHoursLogTypeEnum.CONSUME.getCode()) || item.getLogType().equals(CourseHoursLogTypeEnum.CANCEL.getCode()))
                .map(StoreCourseHoursLog::getTimetableId).toList();

        Map<Long, TimetableVO> timetableMap = new HashMap<>();
        if (CollectionUtil.isNotEmpty(timetableIdList)) {
            R<List<TimetableVO>> listById = remoteTimetableService.getListById(TimetableDTO.builder().idList(timetableIdList).build());
            if (!listById.isOk()) {
                throw new BizException("远程调用Timetable错误");
            }

            //将listById转成Map，处理重复key的情况
            timetableMap = listById.getData().stream()
                    .collect(Collectors.toMap(
                            TimetableVO::getId,
                            item -> item,
                            (existing, replacement) -> replacement
                    ));
        }


        Map<Long, TimetableVO> finalTimetableMap = timetableMap;
        List<CourseHoursLogVO> result =
                pageRecord.getRecords().stream()
                        .map(
                                item -> {
                                    CourseHoursLogVO courseHoursLogVO = new CourseHoursLogVO();
                                    BeanUtil.copyProperties(item, courseHoursLogVO);
                                    if (item.getLogType().equals(CourseHoursLogTypeEnum.CONSUME.getCode()) || item.getLogType().equals(CourseHoursLogTypeEnum.CANCEL.getCode())) {
                                        if (finalTimetableMap.get(item.getTimetableId()) != null) {
                                            courseHoursLogVO.setLessonName(
                                                    finalTimetableMap.get(item.getTimetableId()).getLessonName());
                                        }
                                        // 处理补课情况：通过timetableId判断是否为补课，并获取原课次信息
                                        if (Objects.nonNull(item.getTimetableId())) {
                                            String timetableIdStr = String.valueOf(item.getTimetableId());
                                            if (timetableIdStr.length() > 10) { // 补课表ID的特征
                                                courseHoursLogVO.setIsMakeUpOnline(YesNoEnum.YES.getCode());
                                                // 获取原课次信息
                                                TimetableVO originalTimetable =
                                                        remoteTimetableService.getOriginalTimetable(item.getTimetableId());
                                                if (Objects.nonNull(originalTimetable)) {
                                                    courseHoursLogVO.setLessonName(originalTimetable.getLessonName());
                                                }
                                            }
                                        }
                                    }
                                    return courseHoursLogVO;
                                })
                        .toList();

        return new Page<CourseHoursLogVO>(pageRecord.getCurrent(), pageRecord.getSize(), pageRecord.getTotal()).setRecords(result);
    }

    @Override
    public List<StoreCourseHoursLogVO> getStudentConsumeListByIds(List<Long> ids) {
        if (CollectionUtil.isEmpty(ids)) {
            return Collections.emptyList();
        }
        return list(Wrappers.<StoreCourseHoursLog>lambdaQuery()
                        .in(StoreCourseHoursLog::getTimetableId, ids)
                        .in(StoreCourseHoursLog::getLogType, Lists.asList(CourseHoursLogTypeEnum.CONSUME.getCode(), CourseHoursLogTypeEnum.CANCEL.getCode()))
                //.eq(StoreCourseHoursLog::getLogType, CourseHoursLogTypeEnum.CONSUME.getCode())
        ).stream().map(item -> {
            StoreCourseHoursLogVO storeCourseHoursLogVO = new StoreCourseHoursLogVO();
            BeanUtil.copyProperties(item, storeCourseHoursLogVO);
            return storeCourseHoursLogVO;
        }).toList();
    }

    @Override
    public List<StoreCourseHoursLogDTO> listCourseHoursLog(CourseHoursLogQuery courseHoursLogQuery) {
        log.info("查询课消记录参数:{}", JSON.toJSONString(courseHoursLogQuery));
        if (Objects.isNull(courseHoursLogQuery) || CollectionUtils.isEmpty(courseHoursLogQuery.getTimetableIdList())) {
            throw new BizException("查询条件不能为空");
        }

        //operationType:操作类型: GIFT（赠送课时）,TRIAL（试听课时）,ENROLL（正式课时）
        //log_type: 0（新增试听学员）,1（学员添加课时）,2（学员部分退费）,3（学员全部退费）,4（学员课消）,5（学员取消课消）
        return this.list(Wrappers.<StoreCourseHoursLog>lambdaQuery()
                        .in(StoreCourseHoursLog::getTimetableId, courseHoursLogQuery.getTimetableIdList())
                        .eq(Objects.nonNull(courseHoursLogQuery.getStoreId()), StoreCourseHoursLog::getStoreId, courseHoursLogQuery.getStoreId())
                        .ne(StoreCourseHoursLog::getCourseType, 2)
                        .eq(StoreCourseHoursLog::getLogType, CourseHoursLogTypeEnum.CONSUME.getCode())
                        .eq(StoreCourseHoursLog::getNullify, NullifyEnum.DEFAULT.code))
                .stream().map(storeCourseHoursLog -> {
                    StoreCourseHoursLogDTO storeCourseHoursLogDTO = new StoreCourseHoursLogDTO();
                    BeanUtil.copyProperties(storeCourseHoursLog, storeCourseHoursLogDTO);
                    return storeCourseHoursLogDTO;
                }).collect(Collectors.toList());
    }

    @Override
    public Page<CourseHoursLogNewVO> getCourseList(Page page, CourseHoursLogNewQuery courseHoursLogNewQuery) {
        // 获取门店下的学生信息
        List<Student> studentList = studentMapper.selectList(Wrappers.lambdaQuery(Student.class)
                .select(Student::getUserId, Student::getName, Student::getGrade, Student::getStageId)
                .eq(Student::getSchoolId, courseHoursLogNewQuery.getSchoolId()));
        if (CollectionUtils.isEmpty(studentList)) {
            return new Page<>();
        }
        Map<Long, String> studentNameMap = new HashMap<>();
        Map<Long, Integer> studentStageMap = new HashMap<>();
        for (Student student : studentList) {
            Long userId = student.getUserId();
            studentNameMap.put(userId, student.getName());
            if (student.getStageId() != null) {
                studentStageMap.put(userId, student.getStageId());
            }
        }
        // 查询双师阶段信息
        Map<Integer, String> stageMap = Collections.emptyMap();
        try {
            R<List<StageVO>> stageInfoList = remoteStageService.getStageList(StageProductEnum.STAGE_PRODUCT_ENUM_1.code);
            stageMap = stageInfoList.getData() == null ? Collections.emptyMap() :
                    stageInfoList.getData().stream()
                            .collect(Collectors.toMap(StageVO::getId, StageVO::getStageName));
        } catch (Exception e) {
            log.error("获取阶段信息失败", e);
        }
        Page<StoreCourseHoursLog> courseHoursLogPage = baseMapper.getCoursePage(page, courseHoursLogNewQuery);
        List<StoreCourseHoursLog> records = courseHoursLogPage.getRecords();
        if (CollectionUtils.isEmpty(records)) {
            return new Page<>();
        }
        List<Long> timetableIdList = records.stream().map(StoreCourseHoursLog::getTimetableId).filter(Objects::nonNull).distinct().toList();
        Map<Long, TimetableVO> timetableMap = Collections.emptyMap();
        try {
            List<TimetableVO> timetableList = remoteTimetableService.getTimetableInfoByTimetableIds(timetableIdList,courseHoursLogNewQuery.getStoreId()).getData();
            if (!CollectionUtils.isEmpty(timetableList)) {
                timetableMap = timetableList.stream().collect(Collectors.toMap(TimetableVO::getId, v -> v, (oldValue, newValue) -> oldValue));
            }
        } catch (Exception e) {
            log.error("获取课表信息失败", e);
        }
        // 获取课程信息
        Map<Long, CourseVO> courseMap = Collections.emptyMap();
        List<Long> courseIdList = timetableMap.values().stream()
                .map(TimetableVO::getCourseId)
                .filter(Objects::nonNull)
                .distinct()
                .toList();
        if (!courseIdList.isEmpty()) {
            try {
                R<Map<Long, CourseVO>> courseResult = remoteCourseService.getCourseMapByIdList(courseIdList);
                courseMap = courseResult.getData() == null ? Collections.emptyMap() : courseResult.getData();
            } catch (Exception e) {
                log.error("获取课程信息失败", e);
            }
        }
        //获取课程类型
        R<List<CourseTypeDTO>> courseTypeResult = remoteCourseTypeService.getAll();
        Map<Integer, String> courseTypeMap;
        if (courseTypeResult.isOk()) {
            courseTypeMap = courseTypeResult.getData().stream().collect(Collectors.toMap(CourseTypeDTO::getId, CourseTypeDTO::getName));
        } else {
            courseTypeMap = Collections.emptyMap();
        }
        Map<Integer, String> finalStageMap = stageMap;
        Map<Long, TimetableVO> finalTimetableMap = timetableMap;
        Map<Long, CourseVO> finalCourseMap = courseMap;
        List<CourseHoursLogNewVO> resultRecords = records.stream()
                .map(entity -> {
                    CourseHoursLogNewVO vo = new CourseHoursLogNewVO();
                    BeanUtil.copyProperties(entity, vo);
                    vo.setStudentName(studentNameMap.getOrDefault(entity.getStudentId(), ""));
                    vo.setStageName(finalStageMap.getOrDefault(studentStageMap.getOrDefault(entity.getStudentId(), 0), ""));
                    TimetableVO timetable = finalTimetableMap.get(entity.getTimetableId());
                    vo.setCourseHours(Math.abs(entity.getCourseHours()));
                    BigDecimal amountTotal = entity.getTotalAmount() != null ? entity.getTotalAmount() : BigDecimal.ZERO;
                    BigDecimal truncatedTotal = amountTotal.abs().setScale(2, RoundingMode.DOWN);
                    vo.setUnitPrice(truncatedTotal);
                    vo.setTotalAmount(truncatedTotal);
                    if (timetable != null) {
                        CourseVO course = finalCourseMap.get(timetable.getCourseId());
                        Integer courseDateType = Optional.ofNullable(course).map(CourseVO::getCourseTypeId).map(Math::toIntExact).orElse(0);
                        vo.setCourseDateType(courseDateType);
                        vo.setClassName(timetable.getClassName());
                        vo.setTeacherName(timetable.getTeacherName());
                        vo.setLessonName(timetable.getLessonName());
                        vo.setClassTime(timetable.getClassDate() + "(" + numberToChinese(DateUtils.getWeek(timetable.getClassDate())) + ")" + timetable.getClassStartTime().format(DateTimeFormatter.ofPattern("HH:mm")) + "-" + timetable.getClassEndTime().format(DateTimeFormatter.ofPattern("HH:mm")));
                        vo.setLectureId(timetable.getLectureId());
                        vo.setLectureName(timetable.getLectureName());
                        vo.setCourseDateTypeName(courseTypeMap.get(courseDateType));
                    }
                    vo.setCourseTypeName(courseTypeMap.get(entity.getCourseType()));
                    String operationTypeName = "正式课次";
                    if (Objects.equals(entity.getOperationType(), "GIFT")) {
                        operationTypeName = "赠送课次";
                    }
                    vo.setOperationTypeName(operationTypeName);
                    return vo;
                }).toList();
        Page<CourseHoursLogNewVO> resultPage = new Page<>(page.getCurrent(), page.getSize(), courseHoursLogPage.getTotal());
        resultPage.setRecords(resultRecords);
        return resultPage;
    }

    @Override
    public List<CourseHoursLogNewVO> getCourseAllList(CourseHoursLogNewQuery courseHoursLogNewQuery) {
        // 获取门店下的学生信息
        List<Student> studentList = studentMapper.selectList(Wrappers.lambdaQuery(Student.class)
                .select(Student::getUserId, Student::getName, Student::getGrade, Student::getStageId)
                .eq(Student::getSchoolId, courseHoursLogNewQuery.getSchoolId()));
        if (CollectionUtils.isEmpty(studentList)) {
            return new ArrayList<>();
        }
        Map<Long, String> studentNameMap = new HashMap<>();
        Map<Long, Integer> studentStageMap = new HashMap<>();
        for (Student student : studentList) {
            Long userId = student.getUserId();
            studentNameMap.put(userId, student.getName());
            if (student.getStageId() != null) {
                studentStageMap.put(userId, student.getStageId());
            }
        }
        // 查询双师阶段信息
        Map<Integer, String> stageMap = Collections.emptyMap();
        try {
            R<List<StageVO>> stageInfoList = remoteStageService.getStageList(StageProductEnum.STAGE_PRODUCT_ENUM_1.code);
            stageMap = stageInfoList.getData() == null ? Collections.emptyMap() :
                    stageInfoList.getData().stream()
                            .collect(Collectors.toMap(StageVO::getId, StageVO::getStageName));
        } catch (Exception e) {
            log.error("获取阶段信息失败", e);
        }
        List<StoreCourseHoursLog> records = baseMapper.getCourseList(courseHoursLogNewQuery);
        if (CollectionUtils.isEmpty(records)) {
            return new ArrayList<>();
        }
        List<Long> timetableIdList = records.stream().map(StoreCourseHoursLog::getTimetableId).filter(Objects::nonNull).distinct().toList();
        Map<Long, TimetableVO> timetableMap = Collections.emptyMap();
        try {
            List<TimetableVO> timetableList = remoteTimetableService.getTimetableInfoByTimetableIds(timetableIdList,courseHoursLogNewQuery.getStoreId()).getData();
            if (!CollectionUtils.isEmpty(timetableList)) {
                timetableMap = timetableList.stream().collect(Collectors.toMap(TimetableVO::getId, v -> v, (oldValue, newValue) -> oldValue));
            }
        } catch (Exception e) {
            log.error("获取课表信息失败", e);
        }
        // 获取课程信息
        Map<Long, CourseVO> courseMap = Collections.emptyMap();
        List<Long> courseIdList = timetableMap.values().stream()
                .map(TimetableVO::getCourseId)
                .filter(Objects::nonNull)
                .distinct()
                .toList();
        if (!courseIdList.isEmpty()) {
            try {
                R<Map<Long, CourseVO>> courseResult = remoteCourseService.getCourseMapByIdList(courseIdList);
                courseMap = courseResult.getData() == null ? Collections.emptyMap() : courseResult.getData();
            } catch (Exception e) {
                log.error("获取课程信息失败", e);
            }
        }
        //获取课程类型
        R<List<CourseTypeDTO>> courseTypeResult = remoteCourseTypeService.getAll();
        Map<Integer, String> courseTypeMap;
        if (courseTypeResult.isOk()) {
            courseTypeMap = courseTypeResult.getData().stream().collect(Collectors.toMap(CourseTypeDTO::getId, CourseTypeDTO::getName));
        } else {
            courseTypeMap = Collections.emptyMap();
        }
        Map<Integer, String> finalStageMap = stageMap;
        Map<Long, TimetableVO> finalTimetableMap = timetableMap;
        Map<Long, CourseVO> finalCourseMap = courseMap;
        return records.stream()
                .map(entity -> {
                    CourseHoursLogNewVO vo = new CourseHoursLogNewVO();
                    BeanUtil.copyProperties(entity, vo);
                    vo.setStudentName(studentNameMap.getOrDefault(entity.getStudentId(), ""));
                    vo.setStageName(finalStageMap.getOrDefault(studentStageMap.getOrDefault(entity.getStudentId(), 0), ""));
                    TimetableVO timetable = finalTimetableMap.get(entity.getTimetableId());
                    vo.setCourseHours(Math.abs(entity.getCourseHours()));
                    BigDecimal amountTotal = entity.getTotalAmount() != null ? entity.getTotalAmount() : BigDecimal.ZERO;
                    BigDecimal truncatedTotal = amountTotal.abs().setScale(2, RoundingMode.DOWN);
                    vo.setUnitPrice(truncatedTotal);
                    vo.setTotalAmount(truncatedTotal);
                    if (timetable != null) {
                        CourseVO course = finalCourseMap.get(timetable.getCourseId());
                        Integer courseDateType = Optional.ofNullable(course).map(CourseVO::getCourseTypeId).map(Math::toIntExact).orElse(0);
                        vo.setCourseDateType(courseDateType);
                        vo.setClassName(timetable.getClassName());
                        vo.setTeacherName(timetable.getTeacherName());
                        vo.setLessonName(timetable.getLessonName());
                        vo.setClassTime(timetable.getClassDate() + "(" + numberToChinese(DateUtils.getWeek(timetable.getClassDate())) + ")" + timetable.getClassStartTime().format(DateTimeFormatter.ofPattern("HH:mm")) + "-" + timetable.getClassEndTime().format(DateTimeFormatter.ofPattern("HH:mm")));
                        vo.setLectureId(timetable.getLectureId());
                        vo.setLectureName(timetable.getLectureName());
                        vo.setCourseDateTypeName(courseTypeMap.get(courseDateType));
                    }
                    vo.setCourseTypeName(courseTypeMap.get(entity.getCourseType()));
                    String operationTypeName = "正式课次";
                    if (Objects.equals(entity.getOperationType(), "GIFT")) {
                        operationTypeName = "赠送课次";
                    }
                    vo.setOperationTypeName(operationTypeName);
                    return vo;
                }).toList();
    }



    @Override
    public CourseHoursLogTotalVO getCourseAll(CourseHoursLogNewQuery courseHoursLogNewQuery) {
        List<Map<String, Object>> result = baseMapper.getCourseAll(courseHoursLogNewQuery);
        int courseHoursSum = 0;
        BigDecimal totalAmountSum = BigDecimal.ZERO;

        for (Map<String, Object> map : result) {
            Object courseHoursObj = map.get("course_hours");
            if (courseHoursObj != null && courseHoursObj instanceof Number) {
                courseHoursSum += ((Number) courseHoursObj).intValue();
            }

            Object totalAmountObj = map.get("total_amount");
            if (totalAmountObj != null && totalAmountObj instanceof Number) {
                BigDecimal totalAmount = (BigDecimal) totalAmountObj;
                BigDecimal truncatedAmount = totalAmount.abs().setScale(2, RoundingMode.DOWN);
                totalAmountSum = totalAmountSum.add(truncatedAmount);
            }
        }
        CourseHoursLogTotalVO vo = new CourseHoursLogTotalVO();
        vo.setCourseHoursSum(Math.abs(courseHoursSum));
        vo.setUnitPriceSum(totalAmountSum);
        vo.setTotalAmountSum(totalAmountSum);
        return vo;
    }

    @Override
    public List<CourseHoursLogNewVO> getCourseListExport(CourseHoursLogNewQuery courseHoursLogNewQuery) {
        //获取门店下的学生信息
        List<Student> studentList = studentMapper.selectList(Wrappers.lambdaQuery(Student.class)
                .select(Student::getUserId, Student::getName, Student::getGrade)
                .eq(Student::getStoreId, courseHoursLogNewQuery.getStoreId()));

        if (CollectionUtils.isEmpty(studentList)) {
            return new ArrayList<>();
        }
        Map<Long, String> studentNameMap = studentList.stream().collect(Collectors.toMap(Student::getUserId, Student::getName));
        //获取收费信息
        List<StoreCourseHoursLog> courseHoursLogList = baseMapper.selectList(Wrappers.lambdaQuery(StoreCourseHoursLog.class)
                .eq(Objects.nonNull(courseHoursLogNewQuery.getStoreId()), StoreCourseHoursLog::getStoreId, courseHoursLogNewQuery.getStoreId())
                .eq(StoreCourseHoursLog::getLogType, courseHoursLogNewQuery.getLogType())
                .eq(StoreCourseHoursLog::getNullify, courseHoursLogNewQuery.getNullify())
                .le(Objects.nonNull(courseHoursLogNewQuery.getEndDate()), StoreCourseHoursLog::getCreateTime, courseHoursLogNewQuery.getEndDate())
                .ge(Objects.nonNull(courseHoursLogNewQuery.getStartDate()), StoreCourseHoursLog::getCreateTime, courseHoursLogNewQuery.getStartDate()));
        //获取课表id集合
        List<Long> timetableIdList = courseHoursLogList.stream().map(StoreCourseHoursLog::getTimetableId).toList();
        List<TimetableVO> timetableList = remoteTimetableService.getTimetableInfoByTimetableIds(timetableIdList,courseHoursLogNewQuery.getStoreId()).getData();
        Map<Long, TimetableVO> timetableMap = timetableList.stream()
                .collect(Collectors.toMap(
                        TimetableVO::getId,
                        v -> v,
                        (existing, replacement) -> existing
                ));
        return courseHoursLogList.stream()
                .map(entity -> {
                    CourseHoursLogNewVO vo = new CourseHoursLogNewVO();
                    BeanUtil.copyProperties(entity, vo);
                    vo.setStudentName(studentNameMap.getOrDefault(entity.getStudentId(), ""));
                    TimetableVO timetable = timetableMap.get(entity.getTimetableId());
                    if (timetable != null) {
                        vo.setStageName(timetable.getStageName());
                        vo.setClassName(timetable.getClassName());
                        vo.setTeacherName(timetable.getTeacherName());
                        vo.setLessonName(timetable.getLessonName());
                        vo.setClassTime(timetable.getClassDate() + "(" + numberToChinese(DateUtils.getWeek(timetable.getClassDate())) + ")" + timetable.getClassStartTime().format(DateTimeFormatter.ofPattern("HH:mm")) + "-" + timetable.getClassEndTime().format(DateTimeFormatter.ofPattern("HH:mm")));
                        vo.setLectureId(timetable.getLectureId());
                        vo.setLectureName(timetable.getLectureName());
                    }
                    return vo;
                }).toList();
    }

    @Override
    public StudentCourseHoursLogDTO getByFtUserId(Long ftUserId) {
        Member member = memberMapper.selectById(ftUserId);
        StudentCourseHoursLogDTO studentCourseHoursLogDTO = new StudentCourseHoursLogDTO();
        studentCourseHoursLogDTO.setCourseHours(0);
        studentCourseHoursLogDTO.setStoreCourseHoursLogDTOList(new ArrayList<>());
        if (member == null) {
            log.warn("根据飞天用户ID查询课消记录时，未找到对应的会员信息，ftUserId: {}", ftUserId);
            return studentCourseHoursLogDTO;
        }
        Optional<Student> optionalStudent = studentMapper.selectList(Wrappers.lambdaQuery(Student.class)
                        .eq(Student::getSchStudentId, member.getSchStudentId()))
                .stream().findFirst();
        if (optionalStudent.isEmpty()) {
            log.warn("根据飞天用户ID查询课消记录时，未找到对应的学生信息，ftUserId: {}", ftUserId);
            return studentCourseHoursLogDTO;
        }
        studentCourseHoursLogDTO.setCourseHours(optionalStudent.get().getCourseHours());
        List<StoreCourseHoursLog> storeCourseHoursLogList = courseHoursLogMapper
                .selectList(Wrappers.lambdaQuery(StoreCourseHoursLog.class)
                        .eq(StoreCourseHoursLog::getStudentId, optionalStudent.get().getUserId())
                        .eq(StoreCourseHoursLog::getStoreId, optionalStudent.get().getStoreId())
                        .eq(StoreCourseHoursLog::getLogType, CourseHoursLogTypeEnum.CONSUME.getCode())
                        .eq(StoreCourseHoursLog::getNullify, NullifyEnum.DEFAULT.code)
                        .orderByDesc(StoreCourseHoursLog::getCreateTime)
                        .last("limit 100"));
        //查询课节名称
        List<Long> timeTableIdList = storeCourseHoursLogList
                .stream()
                .map(StoreCourseHoursLog::getTimetableId)
                .distinct()
                .toList();
        List<TimetableVO> timetableVOList = getTimetableInfoByTimetableIds(timeTableIdList,optionalStudent.get().getStoreId());
        Map<Long, TimetableVO> timetableVOMap = timetableVOList
                .stream()
                .collect(Collectors.toMap(TimetableVO::getId, Function.identity()));
        List<Long> courseIdList = timetableVOList
                .stream()
                .map(TimetableVO::getCourseId)
                .distinct()
                .toList();
        //查询课程信息
        Map<Long, List<LessonPubVO>> lessonByCourseIdList = getLessonByCourseIdList(courseIdList);

        List<StoreCourseHoursLogDTO> storeCourseHoursLogDTOList = storeCourseHoursLogList.stream()
                .map(storeCourseHoursLog -> {
                    StoreCourseHoursLogDTO storeCourseHoursLogDTO = new StoreCourseHoursLogDTO();
                    BeanUtil.copyProperties(storeCourseHoursLog, storeCourseHoursLogDTO);
                    TimetableVO timetableVO = timetableVOMap.get(storeCourseHoursLog.getTimetableId());
                    if (timetableVO != null) {
                        List<LessonPubVO> lessonPubVOS = lessonByCourseIdList.get(timetableVO.getCourseId());
                        if (CollectionUtils.isNotEmpty(lessonPubVOS)) {
                            lessonPubVOS.stream().filter(lessonPubVO ->
                                            lessonPubVO.getLessonOrder().equals(timetableVO.getLessonOrder()))
                                    .findFirst().ifPresent(lessonPubVO -> {
                                        storeCourseHoursLogDTO.setLessonName(lessonPubVO.getLessonName());
                                    });
                        }
                    }
                    return storeCourseHoursLogDTO;
                }).toList();
        studentCourseHoursLogDTO.getStoreCourseHoursLogDTOList().addAll(storeCourseHoursLogDTOList);
        return studentCourseHoursLogDTO;
    }

    private Map<Long, List<LessonPubVO>> getLessonByCourseIdList(List<Long> courseIdList) {
        if (CollectionUtils.isEmpty(courseIdList)) {
            return Collections.emptyMap();
        }
        R<Map<Long, List<LessonPubVO>>> response = remoteLessonPubService.getLessonByCourseIdList(courseIdList);
        if (response.isOk()) {
            return response.getData();
        }
        return Collections.emptyMap();
    }

    private List<TimetableVO> getTimetableInfoByTimetableIds(List<Long> timetableIdList,Long storeId) {
        if (CollectionUtils.isEmpty(timetableIdList)) {
            return Collections.emptyList();
        }
        R<List<TimetableVO>> response = remoteTimetableService.getTimetableInfoByTimetableIds(timetableIdList, storeId);
        if (response.isOk()) {
            return response.getData();
        }
        return List.of();
    }

    public static String numberToChinese(int number) {
        return switch (number) {
            case 1 -> "周一";
            case 2 -> "周二";
            case 3 -> "周三";
            case 4 -> "周四";
            case 5 -> "周五";
            case 6 -> "周六";
            case 7 -> "周日";
            default -> String.valueOf(number);
        };
    }

}
