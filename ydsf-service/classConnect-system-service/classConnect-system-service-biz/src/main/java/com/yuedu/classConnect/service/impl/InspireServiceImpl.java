package com.yuedu.classConnect.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yuedu.classConnect.dto.InspireDTO;
import com.yuedu.classConnect.entity.InspireEntity;
import com.yuedu.classConnect.mapper.InspireMapper;
import com.yuedu.classConnect.service.InspireService;
import com.yuedu.classConnect.vo.InspireInfoVO;
import com.yuedu.classConnect.vo.InspireVO;
import com.yuedu.store.api.feign.RemoteStudentService;
import com.yuedu.store.vo.StudentVO;
import com.yuedu.ydsf.common.core.exception.BizException;
import com.yuedu.ydsf.common.core.util.R;
import com.yuedu.ydsf.common.security.util.SecurityUtils;
import com.yuedu.ydsf.eduConnect.jw.api.feign.RemoteClassTimeStudentsService;
import com.yuedu.ydsf.eduConnect.jw.api.vo.CheckInStudentVO;
import com.yuedu.ydsf.eduConnect.live.api.dto.SsCourseStepDTO;
import com.yuedu.ydsf.eduConnect.live.api.dto.SsInteractionSettingDTO;
import com.yuedu.ydsf.eduConnect.live.api.feign.RemoteInteractionService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 激励表
 *
 * <AUTHOR>
 * @date 2025-01-06 10:53:19
 */
@Slf4j
@Service
@AllArgsConstructor
public class InspireServiceImpl extends ServiceImpl<InspireMapper, InspireEntity> implements InspireService {
    private final RemoteInteractionService remoteInteractionService;
    private final RemoteClassTimeStudentsService remoteClassTimeStudentsService;
    private final RemoteStudentService remoteStudentService;
    /**
     * 查询学员列表
     *
     * @return List<RollCallVO>
     */
    @Override
    public List<InspireVO> getList(Integer lessonNo, Integer classId,Long storeId) {
        //调取当前出勤学员列表（已绑定答题器的学生）
        R<List<CheckInStudentVO.StudentCheckInInfoVO>> classResult = remoteClassTimeStudentsService.getCheckedInStudentsByLessonNo(Long.valueOf(lessonNo), storeId);
        List<CheckInStudentVO.StudentCheckInInfoVO> classList = classResult.getData();
        if (classList == null) {
            classList = Collections.emptyList();
        }

        // 查询已鼓励的学生记录
        List<InspireEntity> inspireEntities = baseMapper.selectList(Wrappers.lambdaQuery(InspireEntity.class)
                .eq(InspireEntity::getLessonNo, lessonNo));

        // 获取所有已鼓励学生的ID列表（去重）
        List<Long> inspiredStudentIds = inspireEntities.stream()
                .map(InspireEntity::getUserId)
                .distinct()
                .collect(Collectors.toList());

        // 获取已鼓励学生的详细信息
        Map<Long, StudentVO> inspiredStudentMap = new HashMap<>();
        if (!inspiredStudentIds.isEmpty()) {
            R<List<StudentVO>> studentResult = remoteStudentService.getStudentListByIds(inspiredStudentIds);
            if (studentResult.isOk() && studentResult.getData() != null) {
                inspiredStudentMap = studentResult.getData().stream()
                        .collect(Collectors.toMap(StudentVO::getUserId, student -> student));
            }
        }

        return inspireList(classList, inspireEntities, inspiredStudentMap);
    }
    /**
     * 合并获取激励列表
     *
     * @param classList 当前绑定答题器的学生列表
     * @param inspireEntities 已鼓励的学生记录
     * @param inspiredStudentMap 已鼓励学生信息映射
     * @return
     */
    public List<InspireVO> inspireList(List<CheckInStudentVO.StudentCheckInInfoVO> classList,
                                     List<InspireEntity> inspireEntities,
                                     Map<Long, StudentVO> inspiredStudentMap) {
        List<InspireVO> inspireVos = new ArrayList<>();
        Set<Long> processedStudentIds = new HashSet<>();

        // 统计每个用户的激励信息
        Map<Long, Map<String, Integer>> studentInspireCountMap = new HashMap<>();
        if (CollUtil.isNotEmpty(inspireEntities)) {
            for (InspireEntity inspire : inspireEntities) {
                studentInspireCountMap.computeIfAbsent(inspire.getUserId(), k -> new HashMap<>())
                        .merge(inspire.getType(), 1, Integer::sum);
            }
        }

        // 1. 处理当前绑定答题器的学生
        for (CheckInStudentVO.StudentCheckInInfoVO student : classList) {
            InspireVO inspireVO = new InspireVO();
            Long userId = student.getStudentId();

            inspireVO.setUserId(userId);
            inspireVO.setUserName(student.getStudentName());

            // 获取该学生的激励信息
            List<InspireInfoVO> inspireInfoList = studentInspireCountMap.getOrDefault(userId, Collections.emptyMap())
                    .entrySet().stream().map(entry -> {
                        InspireInfoVO inspireInfoVO = new InspireInfoVO();
                        inspireInfoVO.setType(entry.getKey());
                        inspireInfoVO.setNum(entry.getValue());
                        return inspireInfoVO;
                    }).toList();
            inspireVO.setInspireInfo(inspireInfoList);

            inspireVos.add(inspireVO);
            processedStudentIds.add(userId);
        }

        // 2. 处理已鼓励但当前未绑定答题器的学生
        for (Map.Entry<Long, StudentVO> entry : inspiredStudentMap.entrySet()) {
            Long userId = entry.getKey();
            StudentVO student = entry.getValue();

            // 如果该学生还没有被处理过（即当前未绑定答题器）
            if (!processedStudentIds.contains(userId)) {
                InspireVO inspireVO = new InspireVO();
                inspireVO.setUserId(userId);
                inspireVO.setUserName(student.getName());

                // 获取该学生的激励信息
                List<InspireInfoVO> inspireInfoList = studentInspireCountMap.getOrDefault(userId, Collections.emptyMap())
                        .entrySet().stream().map(entryInner -> {
                            InspireInfoVO inspireInfoVO = new InspireInfoVO();
                            inspireInfoVO.setType(entryInner.getKey());
                            inspireInfoVO.setNum(entryInner.getValue());
                            return inspireInfoVO;
                        }).toList();
                inspireVO.setInspireInfo(inspireInfoList);

                inspireVos.add(inspireVO);
            }
        }

        return inspireVos;
    }

    /**
     * 发送勋章
     *
     * @param inspireDTO 新增发送勋章
     * @return Boolean
     */
    @Override
    public Boolean saveDetail(InspireDTO inspireDTO) {
        //发送消息给教室端
        SsCourseStepDTO ssCourseStepDTO = new SsCourseStepDTO();
        ssCourseStepDTO.setAttendClassType(inspireDTO.getAttendClassType());
        if (inspireDTO.getAttendClassType() == 0) {
            ssCourseStepDTO.setRoomUUID(inspireDTO.getRoomUUID());
        }
        SsInteractionSettingDTO propertiesDTO = new SsInteractionSettingDTO();
        propertiesDTO.setStudentId(String.valueOf(inspireDTO.getUserId()));
        propertiesDTO.setStudentName(inspireDTO.getUserName());
        propertiesDTO.setIncentiveType(inspireDTO.getType());
        propertiesDTO.setCreator(SecurityUtils.getUser().getUsername());
        propertiesDTO.setCreatorId(SecurityUtils.getUser().getId());
        propertiesDTO.setCtime(LocalDateTime.now());
        ssCourseStepDTO.setProperties(propertiesDTO);
        ssCourseStepDTO.setTimeTableId(inspireDTO.getTimeTableId());

        InspireEntity inspireEntity = new InspireEntity();
        BeanUtil.copyProperties(inspireDTO, inspireEntity);

        R response =  remoteInteractionService.medal(ssCourseStepDTO);
        if (response.isOk()) {
            save(inspireEntity);
        } else {
            throw new BizException(response.getMsg());
        }
        return Boolean.TRUE;
    }
    /**
     * 查询获得勋章的学员列表
     *
     * @return List<InspireVO>
     */
    @Override
    public List<InspireVO> getMedalList(Integer lessonNo) {
        // 查询已鼓励的学生记录
        List<InspireEntity> inspireEntities = baseMapper.selectList(Wrappers.lambdaQuery(InspireEntity.class)
                .eq(InspireEntity::getLessonNo, lessonNo));

        // 获取所有已鼓励学生的ID列表（去重）
        List<Long> inspiredStudentIds = inspireEntities.stream()
                .map(InspireEntity::getUserId)
                .distinct()
                .collect(Collectors.toList());

        // 获取已鼓励学生的详细信息
        Map<Long, StudentVO> inspiredStudentMap = new HashMap<>();
        if (!inspiredStudentIds.isEmpty()) {
            R<List<StudentVO>> studentResult = remoteStudentService.getStudentListByIds(inspiredStudentIds);
            if (studentResult.isOk() && studentResult.getData() != null) {
                inspiredStudentMap = studentResult.getData().stream()
                        .collect(Collectors.toMap(StudentVO::getUserId, student -> student));
            }
        }

        return inspireList(Collections.emptyList(), inspireEntities, inspiredStudentMap);
    }
}
