package com.yuedu.ydsf.eduConnect.jw.controller;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;

import com.yuedu.ydsf.common.core.util.R;
import com.yuedu.ydsf.common.core.constant.CommonConstants;
import com.yuedu.ydsf.eduConnect.jw.service.BClassTimeStudentService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
@DisplayName("BClassTimeStudentController通知功能单元测试")
class BClassTimeStudentControllerNotificationTest {

    @Mock
    private BClassTimeStudentService classTimeStudentService;

    @InjectMocks
    private BClassTimeStudentController controller;

    private static final Long TEST_LESSON_NO = 1001L;
    private static final Long TEST_STORE_ID = 100L;

    @BeforeEach
    void setUp() {
        // 初始化测试数据
    }

    @Test
    @DisplayName("成功发送通知给已出勤学生")
    void testSendNotificationToAttendedStudents_Success() {
        // Given
        R<String> expectedResult = R.ok("成功发送3条通知消息");

        when(classTimeStudentService.sendNotificationToAttendedStudents(eq(TEST_LESSON_NO), any(Long.class)))
            .thenReturn(expectedResult);

        // When
        R result = controller.sendNotificationToAttendedStudents(TEST_LESSON_NO);

        // Then
        assertNotNull(result);
        assertEquals(expectedResult.getCode(), result.getCode());
        assertEquals(expectedResult.getData(), result.getData());
    }

    @Test
    @DisplayName("课次编号为空时返回错误")
    void testSendNotificationToAttendedStudents_NullLessonNo() {
        // When
        R result = controller.sendNotificationToAttendedStudents(null);

        // Then
        assertNotNull(result);
        assertEquals(CommonConstants.FAIL, result.getCode());
        assertEquals("课次编号不能为空且必须大于0", result.getMsg());
    }

    @Test
    @DisplayName("课次编号为0时返回错误")
    void testSendNotificationToAttendedStudents_ZeroLessonNo() {
        // When
        R result = controller.sendNotificationToAttendedStudents(0L);

        // Then
        assertNotNull(result);
        assertEquals(CommonConstants.FAIL, result.getCode());
        assertEquals("课次编号不能为空且必须大于0", result.getMsg());
    }

    @Test
    @DisplayName("课次编号为负数时返回错误")
    void testSendNotificationToAttendedStudents_NegativeLessonNo() {
        // When
        R result = controller.sendNotificationToAttendedStudents(-1L);

        // Then
        assertNotNull(result);
        assertEquals(CommonConstants.FAIL, result.getCode());
        assertEquals("课次编号不能为空且必须大于0", result.getMsg());
    }

    @Test
    @DisplayName("服务层抛出异常时返回错误")
    void testSendNotificationToAttendedStudents_ServiceException() {
        // Given
        String errorMessage = "数据库连接失败";

        when(classTimeStudentService.sendNotificationToAttendedStudents(eq(TEST_LESSON_NO), any(Long.class)))
            .thenThrow(new RuntimeException(errorMessage));

        // When
        R result = controller.sendNotificationToAttendedStudents(TEST_LESSON_NO);

        // Then
        assertNotNull(result);
        assertEquals(CommonConstants.FAIL, result.getCode());
        assertEquals("发送通知失败: " + errorMessage, result.getMsg());
    }

    @Test
    @DisplayName("暂无已出勤学生时返回成功")
    void testSendNotificationToAttendedStudents_NoAttendedStudents() {
        // Given
        R<String> expectedResult = R.ok("暂无已出勤学生，无需发送通知");

        when(classTimeStudentService.sendNotificationToAttendedStudents(eq(TEST_LESSON_NO), any(Long.class)))
            .thenReturn(expectedResult);

        // When
        R result = controller.sendNotificationToAttendedStudents(TEST_LESSON_NO);

        // Then
        assertNotNull(result);
        assertEquals(expectedResult.getCode(), result.getCode());
        assertEquals(expectedResult.getData(), result.getData());
    }

    @Test
    @DisplayName("所有学生都已发送过通知时返回成功")
    void testSendNotificationToAttendedStudents_AllAlreadySent() {
        // Given
        R<String> expectedResult = R.ok("所有学生都已发送过通知或未绑定微信");

        when(classTimeStudentService.sendNotificationToAttendedStudents(eq(TEST_LESSON_NO), any(Long.class)))
            .thenReturn(expectedResult);

        // When
        R result = controller.sendNotificationToAttendedStudents(TEST_LESSON_NO);

        // Then
        assertNotNull(result);
        assertEquals(expectedResult.getCode(), result.getCode());
        assertEquals(expectedResult.getData(), result.getData());
    }
}
