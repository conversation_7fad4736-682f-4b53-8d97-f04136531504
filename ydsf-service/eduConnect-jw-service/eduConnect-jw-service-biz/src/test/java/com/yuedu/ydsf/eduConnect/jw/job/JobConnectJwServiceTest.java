package com.yuedu.ydsf.eduConnect.jw.job;

import com.yuedu.ydsf.eduConnect.jw.service.BClassTimeStudentService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import static org.mockito.Mockito.*;

/**
 * JobConnectJwService 单元测试
 * <AUTHOR>
 * @date 2025/1/13 16:34
 */
@ExtendWith(MockitoExtension.class)
public class JobConnectJwServiceTest {

    @InjectMocks
    private JobConnectJwService jobConnectJwService;

    @Mock
    private BClassTimeStudentService classTimeStudentService;

    @BeforeEach
    void setUp() {
        // 如果有需要初始化的内容，可以在这里进行
    }

    /**
     * 测试插入只排课但未点进去考勤列表的课次学生插入班级学生定时任务
     */
    @Test
    void testSaveClassStudentIntoClassTimeStudent() {
        // 执行定时任务
        jobConnectJwService.saveClassStudentIntoClassTimeStudent();

        // 验证是否调用了 classTimeStudentService 的相应方法
        verify(classTimeStudentService, times(1)).saveClassStudentIntoClassTimeStudent();
    }

    /**
     * 测试插入只排课但未点进去考勤列表的课次学生插入班级学生定时任务 - 异常情况
     */
    @Test
    void testSaveClassStudentIntoClassTimeStudent_WhenException() {
        // 模拟服务抛出异常
        doThrow(new RuntimeException("测试异常")).when(classTimeStudentService).saveClassStudentIntoClassTimeStudent();

        // 执行定时任务 - 即使发生异常，任务也应该能够正常完成
        jobConnectJwService.saveClassStudentIntoClassTimeStudent();

        // 验证是否调用了 classTimeStudentService 的相应方法
        verify(classTimeStudentService, times(1)).saveClassStudentIntoClassTimeStudent();
    }
} 