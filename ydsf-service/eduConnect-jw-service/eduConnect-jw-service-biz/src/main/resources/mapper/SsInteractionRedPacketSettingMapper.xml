<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.yuedu.ydsf.eduConnect.jw.mapper.InteractionRedPacketSettingMapper">

  <resultMap id="ssInteractionRedPacketSettingMap" type="com.yuedu.ydsf.eduConnect.jw.entity.InteractionRedPacketSetting">
        <id property="id" column="id"/>
        <result property="source" column="source"/>
        <result property="xgjCampusId" column="xgj_campus_id"/>
        <result property="redPacketNumber" column="red_packet_number"/>
        <result property="redPacketUpperLimit" column="red_packet_upper_limit"/>
        <result property="redPacketLowerLimit" column="red_packet_lower_limit"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="delFlag" column="del_flag"/>
  </resultMap>
</mapper>
