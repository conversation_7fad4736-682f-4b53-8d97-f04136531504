<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.yuedu.ydsf.eduConnect.jw.mapper.SsInteractionConsequenceMapper">

    <resultMap id="ssInteractionConsequenceMap" type="com.yuedu.ydsf.eduConnect.jw.entity.SsInteractionConsequence">
        <id property="id" column="id"/>
        <result property="interactionSettingId" column="interaction_setting_id"/>
        <result property="classTimeId" column="class_time_id"/>
        <result property="deviceId" column="device_id"/>
        <result property="campusId" column="campus_id"/>
        <result property="classRoomId" column="class_room_id"/>
        <result property="studentId" column="student_id"/>
        <result property="studentNo" column="student_no"/>
        <result property="studentName" column="student_name"/>
        <result property="integralNumber" column="integral_number"/>
        <result property="answerOption" column="answer_option"/>
        <result property="ctime" column="ctime"/>
        <result property="creator" column="creator"/>
        <result property="mtime" column="mtime"/>
        <result property="modifer" column="modifer"/>
        <result property="delFlag" column="del_flag"/>
    </resultMap>

    <!-- 检查学生是否有互动数据 -->
    <select id="countStudentInteraction" resultType="long">
        SELECT COUNT(1)
        FROM ss_interaction_consequence
        WHERE class_time_id = #{classTimeId}
          AND student_id = #{studentId}
    </select>

</mapper>
