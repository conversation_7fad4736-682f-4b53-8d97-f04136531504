<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.yuedu.ydsf.eduConnect.jw.mapper.CourseVodMapper">

  <resultMap id="courseVodMap" type="com.yuedu.ydsf.eduConnect.jw.entity.CourseVod">
        <id property="id" column="id"/>
        <result property="storeId" column="store_id"/>
        <result property="name" column="name"/>
        <result property="courseId" column="course_id"/>
        <result property="stage" column="stage"/>
        <result property="lectureId" column="lecture_id"/>
        <result property="classId" column="class_id"/>
        <result property="classroomId" column="classroom_id"/>
        <result property="teacherId" column="teacher_id"/>
        <result property="scheduled" column="scheduled"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="delFlag" column="del_flag"/>
  </resultMap>

    <!--分页查询已约点播课-->
    <select id="getCourseVodPage" parameterType="com.yuedu.ydsf.eduConnect.jw.api.query.CourseVodQuery"
            resultType="com.yuedu.ydsf.eduConnect.jw.api.vo.CourseVodVO">
        SELECT
            t.id,
            t.store_id,
            t.name,
            t.course_id,
            t.stage,
            t.lecture_id,
            t.class_id,
            t.classroom_id,
            t.teacher_id,
            t.scheduled,
            t.create_by,
            t.create_time,
            t.update_by,
            t.update_time,
            t.del_flag
        FROM b_course_vod t
        <where>
            t.del_flag = 0
            <if test="query.storeId != null">
                and t.store_id = #{query.storeId}
            </if>
            <if test="query.lectureId != null">
                and  t.lecture_id = #{query.lectureId}
            </if>
            <if test="query.courseId != null">
                and  t.course_id = #{query.courseId}
            </if>
            <if test="query.stage != null">
                and  t.stage = #{query.stage}
            </if>
            <if test="query.classStartDateTime != null and query.classEndDateTime != null">
                and t.id in (
                    select vod_course_id from b_course_vod_plan
                    where del_flag = 0
                        and class_date between #{query.classStartDateTime} and #{query.classEndDateTime}
                )
            </if>
        </where>
        order by t.create_time desc
    </select>

</mapper>
