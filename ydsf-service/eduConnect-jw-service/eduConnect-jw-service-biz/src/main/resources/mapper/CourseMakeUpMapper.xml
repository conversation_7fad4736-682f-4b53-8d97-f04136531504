<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.yuedu.ydsf.eduConnect.jw.mapper.CourseMakeUpMapper">

    <resultMap id="courseMakeUpMap" type="com.yuedu.ydsf.eduConnect.jw.entity.CourseMakeUp">
        <id property="id" column="id"/>
        <result property="storeId" column="store_id"/>
        <result property="timetableId" column="timetable_id"/>
        <result property="courseId" column="course_id"/>
        <result property="lessonId" column="lesson_id"/>
        <result property="lessonOrder" column="lesson_order"/>
        <result property="timeSlotId" column="time_slot_id"/>
        <result property="lectureId" column="lecture_id"/>
        <result property="classRoomId" column="class_room_id"/>
        <result property="classDate" column="class_date"/>
        <result property="classStartTime" column="class_start_time"/>
        <result property="classEndTime" column="class_end_time"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="delFlag" column="del_flag"/>
    </resultMap>

    <resultMap id="courseMakeUpVOMap" type="com.yuedu.ydsf.eduConnect.jw.api.vo.CourseMakeUpVO">
        <id property="id" column="id"/>
        <result property="storeId" column="store_id"/>
        <result property="timetableId" column="timetable_id"/>
        <result property="courseId" column="course_id"/>
        <result property="lessonOrder" column="lesson_order"/>
        <result property="timeSlotId" column="time_slot_id"/>
        <result property="lectureId" column="lecture_id"/>
        <result property="classRoomId" column="class_room_id"/>
        <result property="classId" column="class_id"/>
        <result property="classDate" column="class_date"/>
        <result property="classStartTime" column="class_start_time"/>
        <result property="classEndTime" column="class_end_time"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="delFlag" column="del_flag"/>
        <result property="makeUpType" column="make_up_type"/>
        <result property="validityStartTime" column="validity_start_time"/>
        <result property="validityEndTime" column="validity_end_time"/>
    </resultMap>

    <select id="selectAllMakeUpWithPage" resultMap="courseMakeUpVOMap">
        SELECT
                id,
                store_id,
                timetable_id,
                course_id,
                teaching_plan_id,
                lesson_order,
                time_slot_id,
                lecture_id,
                class_room_id,
                class_id,
                class_date,
                class_start_time,
                class_end_time,
                create_by,
                create_time,
                update_by,
                update_time,
                del_flag,
                make_up_type,
                validity_start_time,
                validity_end_time
        FROM (
                SELECT
                    a.id,
                    a.store_id,
                    a.timetable_id,
                    a.course_id,
                    a.teaching_plan_id,
                    a.lesson_order,
                    a.time_slot_id,
                    a.lecture_id,
                    a.class_room_id,
                    a.class_id,
                    a.class_date,
                    a.class_start_time,
                    a.class_end_time,
                    a.create_by,
                    a.create_time,
                    a.update_by,
                    a.update_time,
                    a.del_flag,
                    3 as make_up_type,
                    NULL as validity_start_time,
                    NULL as validity_end_time
                    FROM
                    b_course_make_up a
                    WHERE
                    a.store_id = #{query.storeId}
                    <if test="query.lectureId != null">
                        AND a.lecture_id = #{query.lectureId}
                    </if>
                    <if test="query.teachingPlanIdList != null and query.teachingPlanIdList.size() > 0">
                        AND a.teaching_plan_id IN
                        <foreach collection="query.teachingPlanIdList" item="teachingPlanId" open="(" close=")" separator=",">
                            #{teachingPlanId}
                        </foreach>

                    </if>
                    AND a.del_flag = 0
                    UNION ALL
                    SELECT
                        b.id,
                        b.store_id,
                        NULL as timetable_id,
                        b.course_id,
                        b.teaching_plan_id,
                        b.lesson_order,
                        b.time_slot_id,
                        b.lecture_id,
                        b.class_room_id,
                        b.class_id,
                        b.class_date,
                        b.class_start_time,
                        b.class_end_time,
                        b.create_by,
                        b.create_time,
                        b.update_by,
                        b.update_time,
                        b.del_flag,
                        4 as make_up_type,
                        b.validity_start_time,
                        b.validity_end_time
                    FROM
                    b_course_make_up_online b
                    WHERE
                    b.store_id = #{query.storeId}
                    <if test="query.lectureId != null">
                        AND b.lecture_id = #{query.lectureId}
                    </if>
                    <if test="query.teachingPlanIdList != null and query.teachingPlanIdList.size() > 0">
                        AND b.teaching_plan_id IN
                        <foreach collection="query.teachingPlanIdList" item="teachingPlanId" open="(" close=")" separator=",">
                            #{teachingPlanId}
                        </foreach>

                    </if>
                    AND b.del_flag = 0
                ) AS t
        ORDER BY t.create_time DESC
    </select>
</mapper>
