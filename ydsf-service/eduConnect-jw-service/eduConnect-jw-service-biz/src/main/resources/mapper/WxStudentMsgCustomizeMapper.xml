<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.yuedu.ydsf.eduConnect.jw.mapper.WxStudentMsgMapper">

    <!-- 其他SQL语句可以在这里添加 -->
    <insert id="insertBatchIgnoreDuplicateEntry" parameterType="java.util.List">
        INSERT IGNORE INTO wx_student_msg (app_name, school_id, store_id, student_id, obj_id,
        obj_type, class_start_time, type, rep_type, rep_event, rep_content, rep_media_id, rep_name,
        rep_desc, rep_url,
        content, send_status, read_flag, app_id, open_id, remark)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.appName}, #{item.schoolId}, #{item.storeId}, #{item.studentId}, #{item.objId},
            #{item.objType}, #{item.classStartTime}, #{item.type}, #{item.repType},
            #{item.repEvent}, #{item.repContent}, #{item.repMediaId}, #{item.repName},
            #{item.repDesc}, #{item.repUrl}, #{item.content}, #{item.sendStatus}, #{item.readFlag},
            #{item.appId}, #{item.openId}, #{item.remark})
        </foreach>
    </insert>
</mapper>
