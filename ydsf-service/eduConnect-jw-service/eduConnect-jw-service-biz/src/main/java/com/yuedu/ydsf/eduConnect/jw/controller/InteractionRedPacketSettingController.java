package com.yuedu.ydsf.eduConnect.jw.controller;

import com.yuedu.ydsf.common.core.util.R;
import com.yuedu.ydsf.common.log.annotation.SysLog;
import com.yuedu.ydsf.common.security.annotation.StorePermission;
import com.yuedu.ydsf.eduConnect.jw.api.dto.InteractionRedPacketSettingDTO;
import com.yuedu.ydsf.eduConnect.jw.api.query.InteractionRedPacketSettingQuery;
import com.yuedu.ydsf.eduConnect.jw.api.valid.InteractionRedPacketSettingValidGroup;
import com.yuedu.ydsf.eduConnect.jw.api.vo.InteractionRedPacketSettingInfoVO;
import com.yuedu.ydsf.eduConnect.jw.service.InteractionRedPacketSettingService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 门店红包规则设置表 控制类
 *
 * <AUTHOR>
 * @date 2024-11-04 08:41:26
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/interactionRedPacketSetting" )
@Tag(description = "ss_interaction_red_packet_setting" , name = "门店红包规则设置表管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
@StorePermission
public class InteractionRedPacketSettingController {

    private final InteractionRedPacketSettingService ssInteractionRedPacketSettingService;

    /**
     * 约读领航小程序-查询门店红包设置
     * @param ssInteractionRedPacketSettingQuery
     * @return com.yuedu.ydsf.common.core.util.R<com.yuedu.ydsf.eduConnect.api.vo.SsInteractionRedPacketSettingInfoVO>
     * <AUTHOR>
     * @date 2024/11/4 9:42
     */
    @Operation(summary = "约读领航小程序-查询门店红包设置" , description = "约读领航小程序-查询门店红包设置" )
    @GetMapping("/getInteractionRedPacketSettingBySource")
    public R<InteractionRedPacketSettingInfoVO> getInteractionRedPacketSettingBySource(@Validated(InteractionRedPacketSettingValidGroup.GetInteractionRedPacketSettingBySourceGroup.class) @ParameterObject InteractionRedPacketSettingQuery ssInteractionRedPacketSettingQuery) {
        return R.ok(ssInteractionRedPacketSettingService.getInteractionRedPacketSettingBySource(ssInteractionRedPacketSettingQuery));
    }

    /**
     * 约读领航小程序-门店红包规则设置
     * @param ssInteractionRedPacketSettingDTO
     * @return com.yuedu.ydsf.common.core.util.R
     * <AUTHOR>
     * @date 2024/11/4 9:42
     */
    @Operation(summary = "约读领航小程序-门店红包规则设置" , description = "约读领航小程序-门店红包规则设置" )
    @SysLog("约读领航小程序-门店红包规则设置" )
    @PostMapping("/interactionRedPacketSetting")
    public R interactionRedPacketSetting(@Validated(InteractionRedPacketSettingValidGroup.InteractionRedPacketSettingGroup.class) @RequestBody InteractionRedPacketSettingDTO ssInteractionRedPacketSettingDTO) {
        ssInteractionRedPacketSettingService.interactionRedPacketSetting(ssInteractionRedPacketSettingDTO);
        return R.ok("设置成功!");
    }


}
