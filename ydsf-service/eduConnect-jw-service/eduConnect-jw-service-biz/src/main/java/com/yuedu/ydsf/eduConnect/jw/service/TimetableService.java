package com.yuedu.ydsf.eduConnect.jw.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yuedu.ydsf.eduConnect.api.vo.LiveRoomPlanDetailVersionVO;
import com.yuedu.ydsf.eduConnect.jw.api.dto.CourseLiveDTO;
import com.yuedu.ydsf.eduConnect.jw.api.dto.TimetableDTO;
import com.yuedu.ydsf.eduConnect.jw.api.query.TimetableQuery;
import com.yuedu.ydsf.eduConnect.jw.api.vo.TimeTypeTimetableVO;
import com.yuedu.ydsf.eduConnect.jw.api.vo.TimetableClassTimeVO;
import com.yuedu.ydsf.eduConnect.jw.api.vo.TimetableVO;
import com.yuedu.ydsf.eduConnect.jw.entity.Timetable;
import java.time.LocalDate;
import java.util.List;

/**
 * 门店课表 服务类
 *
 * <AUTHOR>
 * @date 2024-12-09 10:27:50
 */
public interface TimetableService extends IService<Timetable> {


    /**
     * 获取课程时间表
     *
     * @param timetableQuery 查询条件
     * @return List<LocalDate>
     */
    List<LocalDate> getTimetableClassDateList(TimetableQuery timetableQuery);

    /**
     * 根据日期获取课程时间表
     *
     * @param timetableDTO 日期
     * @return List<TimeTypeTimetableVO>
     */
    List<TimeTypeTimetableVO> getTimeTableByDate(TimetableDTO timetableDTO);

    /**
     * 根据课表ID获取课表详情
     *
     * @param timetableId
     * @return com.yuedu.ydsf.eduConnect.jw.api.vo.TimetableVO
     * <AUTHOR>
     * @date 2024/12/17 16:53
     */
    TimetableVO getTimetableInfoById(Long timetableId);

    /**
     * 根据课表IDs获取课表列表详情
     *
     * @param timetableId
     * @return com.yuedu.ydsf.eduConnect.jw.api.vo.TimetableVO
     * <AUTHOR>
     * @date 2025-06-11 16:43
     */
    List<TimetableVO> getTimetableInfoByIds(List<Long> timetableId,Long storeId);

    /**
     * 组装课表数据
     *
     * @param addCourseLiveDTO      直播课信息
     * @param roomPlanDetailVersionVO 教学计划详情
     * @param courseLiveId          直播课Id
     * @param courseType            课程类型: 1-直播课; 2-点播课; 3-补课
     */
    Timetable setTimeTable(CourseLiveDTO addCourseLiveDTO,
        LiveRoomPlanDetailVersionVO roomPlanDetailVersionVO, Long courseLiveId, Integer courseType,
        Long storeId, Long lectureIdMain);

    /**
     * 教学计划主讲老师修改同步更新已约课表
     * @return void
     * <AUTHOR>
     * @date 2025/1/13 16:45
     */
    void editTimetableLecture();

    /**
     * 查询门店下老师是否存在未结束课程
     * @return true: 存在; false: 不存在
     * <AUTHOR>
     * @date 2025/2/11 11:12
     */
    boolean existNotFinishedByTeacherId(TimetableDTO timetableDTO);

    /**
     * 查询班级下是否存在排课课程
     * @param timetableDTO
     * @return true: 存在; false: 不存在
     * <AUTHOR>
     * @date 2025/2/12 15:16
     */
    boolean existScheduleCourseByClassId(TimetableDTO timetableDTO);

  /**
   * 通过班级获取课程大纲
   *
   * <AUTHOR>
   * @date 2025/2/13 13:52
   * @param classId
   * @return java.util.List<com.yuedu.ydsf.eduConnect.jw.api.vo.TimetableVO>
   */
  IPage<TimetableVO> getTimetableInfoByClassId(Page page, Long classId);

    /**
     *  直播约课明细列表
     *
     * <AUTHOR>
     * @date 2025年02月25日 19时40分
     */
    IPage<TimetableVO> livePageDetails(Page page, Long coursePlanId);

    /**
     * 查询教室是否存在排课课程
     * @param timetableDTO DTO
     * @return Boolean
     */
    Boolean existScheduleCourseByClassRoomId(TimetableDTO timetableDTO);

    /**
     * 根据ID获取排课列表
     * @param timetableIdList DTO
     * @return List<TimetableVO>
     */
    List<TimetableVO> getListById(List<Long> timetableIdList);

    /**
     * 根据ID列表获取排课列表
     * @param ids ids
     * @return List<TimetableVO>
     */
    List<TimetableClassTimeVO> getListByIds(List<Long> ids);

    /**
     * 根据上课日期查询课次ID
     */
    List<Long> listTimeTableIdByClassDate(TimetableQuery timetableQuery);

    /**
     * 根据上课日期查询课表
     * @param timetableQuery 查询条件
     * @return
     */
    List<Timetable> listTimetableByClassDate(TimetableQuery timetableQuery);

    /**
     * 根据补课id找出对应的原上课的课次相关信息
     * <AUTHOR>
     * @date 2025/4/29 10:13
     * @param makeUpOnLineId
     * @return com.yuedu.ydsf.eduConnect.jw.api.vo.TimetableVO
     */
    TimetableVO getOriginalTimetable(Long makeUpOnLineId);

    /**
     * 获取门店下已结束的课程
     * <AUTHOR>
     * @date 2025/5/7 15:19
     * @param page
     * @param timetableQuery
     * @return com.baomidou.mybatisplus.core.metadata.IPage<com.yuedu.ydsf.eduConnect.jw.api.vo.TimetableVO>
     */
    IPage<TimetableVO> getFinishTimetable(Page page, TimetableQuery timetableQuery);


    /**
     *  根据门店id获取课程列表
     *
     * <AUTHOR>
     * @date 2025年06月05日 16时29分
     */
    List<Long> getCourseListByStoreId(Long storeId);
}
