package com.yuedu.ydsf.eduConnect.jw.controller;

import cn.hutool.json.JSONUtil;
import com.yuedu.teaching.vo.CourseVO;
import com.yuedu.ydsf.common.core.util.R;
import com.yuedu.ydsf.common.security.annotation.StorePermission;
import com.yuedu.ydsf.eduConnect.jw.api.query.AfterClassFeedbackQuery;
import com.yuedu.ydsf.eduConnect.jw.proxy.dto.*;
import com.yuedu.ydsf.eduConnect.jw.proxy.service.ReadingPartyProxyService;
import com.yuedu.ydsf.eduConnect.jw.service.BClassTimeStudentService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;

import java.util.List;
import java.util.Objects;

import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 课后反馈接口
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping(value = "/afterClassFeedback")
@Tag(description = "after_class_feedback", name = "课后反馈")
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class AfterClassFeedbackController {

    private final ReadingPartyProxyService readingPartyProxyService;
    private final BClassTimeStudentService bClassTimeStudentService;


    /**
     * 课后反馈详情
     */
    @Operation(summary = "课后反馈详情", description = "课后反馈详情")
    @StorePermission
    @GetMapping(value = "/detail")
    public R<AfterClassFeedbackDTO> eduConnectSchoolDirectory(
            @ParameterObject AfterClassFeedbackQuery afterClassFeedbackQuery) {
        //查询课次中校管家学生ID
        List<Long> studentIdList = bClassTimeStudentService.getStudentIdsByLessonNo(
                afterClassFeedbackQuery.getLessonNo());

        AfterClassFeedbackDTO afterClassFeedbackDTO = readingPartyProxyService.eduSchoolDirectory(
                afterClassFeedbackQuery, studentIdList);
        log.info("查询课后反馈详情，结果：{}", JSONUtil.toJsonStr(afterClassFeedbackDTO));
        CourseVO courseInfo = bClassTimeStudentService.getCourseInfo(afterClassFeedbackQuery.getCourseId());
        if (Objects.nonNull(courseInfo) && Objects.nonNull(afterClassFeedbackDTO)
                && CollectionUtils.isNotEmpty(afterClassFeedbackDTO.getFtStudentVoList())) {
            afterClassFeedbackDTO.getFtStudentVoList().forEach(studentVo -> {
                studentVo.setStage(courseInfo.getStageId());
            });
        }
        return R.ok(afterClassFeedbackDTO);
    }

    /**
     * 题目多少关
     */
    @Operation(summary = "题目多少关", description = "题目多少关")
    @StorePermission
    @GetMapping(value = "/questionLevel")
    public R<DirectoryLevelResp> directoryLevel(
            @RequestParam(value = "directoryId") Integer directoryId) {
        return R.ok(readingPartyProxyService.getDirectoryLevel(directoryId));
    }

    /**
     * 获取图书题目详情
     */
    @Operation(summary = "获取图书题目详情", description = "获取图书题目详情")
    @StorePermission
    @GetMapping(value = "/questionDetail")
    public R<BookTopicDetailResp> bookTopicDetail(
            @RequestParam(value = "directoryId") Integer directoryId,
            @RequestParam(value = "lessonNo") Long lessonNo,
            @RequestParam(value = "level") Integer level) {
        ReadingPartyDto readingPartyDto = new ReadingPartyDto();
        readingPartyDto.setDirectoryId(directoryId);
        readingPartyDto.setBoundClassId(lessonNo);
        readingPartyDto.setLevel(level);
        //查询课次中校管家学生ID
        List<Long> studentIdList = bClassTimeStudentService.getStudentIdsByLessonNo(lessonNo);
        return R.ok(readingPartyProxyService.bookTopicDetail(readingPartyDto, studentIdList));
    }

    /**
     * 获取答题闯关记录
     */
    @Operation(summary = "获取答题闯关记录", description = "获取答题闯关记录")
    @StorePermission
    @GetMapping(value = "/getStudentAnswerRecord")
    public R<PersonalAnswerRecordResp> getAnswerRecord(
            @RequestParam(value = "directoryId") Integer directoryId,
            @RequestParam(value = "studentId") Integer studentId) {
        ReadingPartyDto readingPartyDto = new ReadingPartyDto();
        readingPartyDto.setDirectoryId(directoryId);
        readingPartyDto.setUserId(studentId);
        return R.ok(readingPartyProxyService.getStudentAnswerRecord(
                readingPartyDto));
    }

    /**
     * 获取学生的反馈详情
     */
    @Operation(summary = "获取学生的反馈详情", description = "获取学生的反馈详情")
    @StorePermission
    @GetMapping(value = "/studentFeedbackDetail")
    public R<ClassBookFeedback> getClassStudentFeedbackLog(
            @RequestParam(value = "directoryId") Integer directoryId,
            @RequestParam(value = "lessonNo") Long lessonNo,
            @RequestParam(value = "studentId") Integer studentId) {
        ReadingPartyDto readingPartyDto = new ReadingPartyDto();
        readingPartyDto.setDirectoryId(directoryId);
        readingPartyDto.setBoundClassId(lessonNo);
        readingPartyDto.setFeedbackUserId(studentId);
        ClassBookFeedback classStudentFeedback = readingPartyProxyService.getClassStudentFeedbackLog(
                readingPartyDto);
        return R.ok(classStudentFeedback);
    }

    /**
     * 保存课后反馈
     */
    @Operation(summary = "保存课后反馈", description = "保存课后反馈")
    @StorePermission
    @PostMapping(value = "/saveFeedbackBook")
    public R saveFeedbackBook(
            @Validated @NotNull @RequestBody ClassBookFeedBackDto classBookFeedBackDto) {
        readingPartyProxyService.saveFeedbackBook(classBookFeedBackDto);
        return R.ok();
    }

    /**
     * 更新课后反馈
     */
    @Operation(summary = "更新课后反馈", description = "更新课后反馈")
    @StorePermission
    @PostMapping(value = "/updateFeedbackBook")
    public R updateFeedbackBook(
            @Validated @NotNull @RequestBody ClassBookFeedBackDto classBookFeedBackDto) {
        readingPartyProxyService.updateFeedbackBook(classBookFeedBackDto);
        return R.ok();
    }

    /**
     * 撤回课后反馈
     */
    @Operation(summary = "撤回课后反馈", description = "撤回课后反馈")
    @StorePermission
    @PostMapping(value = "/delFeedbackBook")
    public R delFeedbackBook(
            @Validated @NotNull @RequestBody ClassBookFeedBackDto classBookFeedBackDto) {
        readingPartyProxyService.delFeedbackBook(classBookFeedBackDto.getFeedbackId());
        return R.ok();
    }

    /**
     * 获取图书详情
     */
    @Operation(summary = "获取图书详情", description = "获取图书详情")
    @StorePermission
    @GetMapping(value = "/directoryDetail")
    public R<DirectoryDetailDto> directoryDetail(
            @RequestParam(value = "directoryId") Integer directoryId) {
        return R.ok(readingPartyProxyService.getDirectoryDetail(directoryId));
    }

    /**
     * 获取阿里云AI问答内容
     */
    @Operation(summary = "获取阿里云AI问答内容", description = "获取阿里云AI问答内容")
    @StorePermission
    @PostMapping(value = "/getAliyunAiContent")
    public R<String> getAliyunAiContent(
            @RequestBody BFeedbackAiLogDto bFeedbackAiLogDto) {
        String aliyunAiContent = readingPartyProxyService.getAliyunAiContent(bFeedbackAiLogDto);
        return R.ok(aliyunAiContent, "查询成功!");
    }

    /**
     * 查询门店店长ID
     */
    @Operation(summary = "查询门店店长ID", description = "查询门店店长ID")
    @StorePermission
    @GetMapping(value = "/getStoreManagerId")
    public R<Integer> getStoreManagerId() {
        return R.ok(readingPartyProxyService.getStoreManagerId());
    }

}
