package com.yuedu.ydsf.eduConnect.jw.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yuedu.permission.api.feign.RemoteLecturerInfoService;
import com.yuedu.permission.api.vo.LecturerInfoVO;
import com.yuedu.store.api.feign.RemoteClassRoomService;
import com.yuedu.store.api.feign.RemoteClassService;
import com.yuedu.store.api.feign.RemoteEmployeeService;
import com.yuedu.store.vo.ClassRoomVO;
import com.yuedu.store.vo.ClassVO;
import com.yuedu.store.vo.EmployeeVO;
import com.yuedu.teaching.api.feign.RemoteCourseService;
import com.yuedu.teaching.api.feign.RemoteLessonService;
import com.yuedu.teaching.api.feign.RemoteStageService;
import com.yuedu.teaching.dto.CourseDTO;
import com.yuedu.teaching.dto.LessonOrderDTO;
import com.yuedu.teaching.entity.LessonEntity;
import com.yuedu.teaching.query.CoursePublishQuery;
import com.yuedu.teaching.vo.CourseVO;
import com.yuedu.teaching.vo.LessonVO;
import com.yuedu.teaching.vo.StageVO;
import com.yuedu.ydsf.common.core.constant.enums.YesNoEnum;
import com.yuedu.ydsf.common.core.exception.BizException;
import com.yuedu.ydsf.common.core.util.R;
import com.yuedu.ydsf.common.security.util.StoreContextHolder;
import com.yuedu.ydsf.eduConnect.api.constant.CheckInStatusEnum;
import com.yuedu.ydsf.eduConnect.api.constant.ClassTimeTypeEnum;
import com.yuedu.ydsf.eduConnect.api.constant.CourseTypeEnum;
import com.yuedu.ydsf.eduConnect.api.constant.StageProductEnum;
import com.yuedu.ydsf.eduConnect.api.constant.TeachingPlanDetailPubMinuteEnum;
import com.yuedu.ydsf.eduConnect.api.constant.TeachingPlanDetailPubStateEnum;
import com.yuedu.ydsf.eduConnect.api.feign.RemoteClassTimeService;
import com.yuedu.ydsf.eduConnect.api.feign.RemoteTeachingPlanDetailPubService;
import com.yuedu.ydsf.eduConnect.api.vo.ClassTimeVO;
import com.yuedu.ydsf.eduConnect.api.vo.LiveRoomPlanDetailVersionVO;
import com.yuedu.ydsf.eduConnect.api.vo.TeachingPlanDetailPubVO;
import com.yuedu.ydsf.eduConnect.jw.api.dto.CourseLiveDTO;
import com.yuedu.ydsf.eduConnect.jw.api.dto.TimetableDTO;
import com.yuedu.ydsf.eduConnect.jw.api.query.TimetableQuery;
import com.yuedu.ydsf.eduConnect.jw.api.vo.AttendanceManagementVO;
import com.yuedu.ydsf.eduConnect.jw.api.vo.TimeTypeTimetableVO;
import com.yuedu.ydsf.eduConnect.jw.api.vo.TimetableClassTimeVO;
import com.yuedu.ydsf.eduConnect.jw.api.vo.TimetableVO;
import com.yuedu.ydsf.eduConnect.jw.entity.BClassTimeStudent;
import com.yuedu.ydsf.eduConnect.jw.entity.BCourseMakeUpOnline;
import com.yuedu.ydsf.eduConnect.jw.entity.CourseMakeUp;
import com.yuedu.ydsf.eduConnect.jw.entity.CourseVod;
import com.yuedu.ydsf.eduConnect.jw.entity.CourseVodPlan;
import com.yuedu.ydsf.eduConnect.jw.entity.Timetable;
import com.yuedu.ydsf.eduConnect.jw.manager.CourseLiveManager;
import com.yuedu.ydsf.eduConnect.jw.mapper.BClassTimeStudentMapper;
import com.yuedu.ydsf.eduConnect.jw.mapper.BCourseMakeUpOnlineMapper;
import com.yuedu.ydsf.eduConnect.jw.mapper.CourseLiveMapper;
import com.yuedu.ydsf.eduConnect.jw.mapper.CourseMakeUpMapper;
import com.yuedu.ydsf.eduConnect.jw.mapper.CourseVodMapper;
import com.yuedu.ydsf.eduConnect.jw.mapper.CourseVodPlanMapper;
import com.yuedu.ydsf.eduConnect.jw.mapper.TimetableMapper;
import com.yuedu.ydsf.eduConnect.jw.service.TimetableService;
import jakarta.annotation.Resource;
import java.time.DayOfWeek;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.format.TextStyle;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 门店课表 服务类
 *
 * <AUTHOR>
 * @date 2024-12-09 10:27:50
 */
@Slf4j
@Service
@AllArgsConstructor
public class TimetableServiceImpl extends ServiceImpl<TimetableMapper, Timetable> implements
    TimetableService {

    @Resource
    private RemoteCourseService remoteCourseService;
    @Resource
    private RemoteLessonService remoteLessonService;
    @Resource
    private RemoteEmployeeService remoteEmployeeService;
    @Resource
    private RemoteClassRoomService remoteClassRoomService;
    @Resource
    private RemoteLecturerInfoService remoteLecturerInfoService;
    @Resource
    private RemoteClassService remoteClassService;
    @Resource
    private RemoteStageService remoteStageService;
    @Resource
    private RemoteClassTimeService remoteClassTimeService;
    @Resource
    private CourseLiveMapper courseLiveMapper;
    @Resource
    private RemoteTeachingPlanDetailPubService remoteTeachingPlanDetailPubService;
    @Resource
    private CourseVodPlanMapper courseVodPlanMapper;
    @Resource
    private CourseMakeUpMapper courseMakeUpMapper;
    @Resource
    private CourseVodMapper courseVodMapper;
    @Autowired
    private TimetableMapper timetableMapper;
    @Autowired
    private CourseLiveManager courseLiveManager;
    @Autowired
    private BCourseMakeUpOnlineMapper bCourseMakeUpOnlineMapper;
    @Autowired
    private BClassTimeStudentMapper bClassTimeStudentMapper;


    /**
     * 获取课程时间表
     *
     * @param timetableQuery 查询条件
     * @return List<LocalDate>
     */
    @Override
    public List<LocalDate> getTimetableClassDateList(TimetableQuery timetableQuery) {
        log.info("获取课程时间表参数: {}", JSONObject.toJSONString(timetableQuery));

        // 不传日期默认返回空
        if (Objects.isNull(timetableQuery.getSelectStartClassDate())
            || Objects.isNull(timetableQuery.getSelectEndClassDate())) {
            return List.of();
        }

        // 取出开始时间，结束时间，查询该日期范围内，哪天有课
        List<Timetable> distinctClassDate = this.list(Wrappers.query(Timetable.class)
            .select("DISTINCT class_date").lambda()
            .between(Timetable::getClassDate, timetableQuery.getSelectStartClassDate(),
                timetableQuery.getSelectEndClassDate())
            .eq(Timetable::getStoreId, timetableQuery.getStoreId())
            .orderByAsc(Timetable::getClassDate)
        );

        return distinctClassDate.stream().map(Timetable::getClassDate).toList();
    }

    /**
     * 根据日期获取课程时间表
     *
     * @param timetableDTO 日期
     * @return List<TimetableVO>
     */
    @Override
    public List<TimeTypeTimetableVO> getTimeTableByDate(TimetableDTO timetableDTO) {
        log.info("根据日期获取课表列表入参: {}", JSONObject.toJSONString(timetableDTO));
        List<Timetable> timetableList = this.list(Wrappers.lambdaQuery(Timetable.class)
            .eq(Timetable::getClassDate, timetableDTO.getClassDate())
            .eq(Timetable::getStoreId, timetableDTO.getStoreId())
            .eq(Objects.nonNull(timetableDTO.getTeacherId()), Timetable::getTeacherId,
                timetableDTO.getTeacherId())
            .orderByAsc(Timetable::getClassStartDateTime)
        );

        // 封装其他服务查询数据
        List<TimetableVO> timetableVOList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(timetableList)) {
            timetableVOList = courseMakeUpVoParam(timetableList);
        }

        // 数据转换处理
        return classTimeTypeDispose(timetableVOList);
    }

    /**
     * 数据转换处理
     *
     * @param timetableVOList
     * @return java.util.List<com.yuedu.ydsf.eduConnect.jw.api.vo.TimeTypeTimetableVO>
     * <AUTHOR>
     * @date 2024/12/18 11:33
     */
    private List<TimeTypeTimetableVO> classTimeTypeDispose(List<TimetableVO> timetableVOList) {

        // 所有时段类型集合
        Set<Integer> allTimeSlotTypes = new HashSet<>(Arrays.asList(
            ClassTimeTypeEnum.CLASS_TIME_TYPE_ENUM_1.code,
            ClassTimeTypeEnum.CLASS_TIME_TYPE_ENUM_2.code,
            ClassTimeTypeEnum.CLASS_TIME_TYPE_ENUM_3.code));

        // 使用 Map 来收集分组结果，初始值为空列表
        Map<Integer, List<TimetableVO>> groupedByTimeSlotType = new HashMap<>();
        allTimeSlotTypes.forEach(type -> groupedByTimeSlotType.put(type, new ArrayList<>()));

        // 从原始列表中填充分组结果
        timetableVOList.stream()
            .filter(e -> Objects.nonNull(e.getTimeSlotType()))
            .forEach(e -> {
                Integer timeSlotType = e.getTimeSlotType();
                if (allTimeSlotTypes.contains(timeSlotType)) {
                    groupedByTimeSlotType.get(timeSlotType).add(e);
                }
            });

        // map转视图类
        List<TimeTypeTimetableVO> timeTypeTimetableVOList = groupedByTimeSlotType.entrySet()
            .stream()
            .map(entry -> {
                TimeTypeTimetableVO timeTypeVO = new TimeTypeTimetableVO();
                timeTypeVO.setTimeType(entry.getKey());
                timeTypeVO.setTimetableVOList(entry.getValue());
                return timeTypeVO;
            })
            .collect(Collectors.toList());

        return timeTypeTimetableVOList;
    }

    /**
     * 根据课表ID获取课表详情
     *
     * @param timetableId
     * @return TimetableVO
     */
    @Override
    public TimetableVO getTimetableInfoById(Long timetableId) {
        String timetableIdStr = String.valueOf(timetableId);
        List<Timetable> timetableList;
        if (timetableIdStr.length() > 10) {
            List<BCourseMakeUpOnline> makeupList = bCourseMakeUpOnlineMapper.selectByIdsWithoutDelFlag(Collections.singletonList(timetableId));
            Timetable timetableInfo = this.getOne(Wrappers.lambdaQuery(Timetable.class)
                .eq(Timetable::getLessonNo, makeupList.get(0).getLessonNo()));
            timetableList = (timetableInfo != null) ? Arrays.asList(timetableInfo) : Collections.emptyList();
            makeupList.forEach(makeup -> {
                Timetable timetable = timetableList.stream()
                   .filter(t -> t.getLessonNo().equals(makeup.getLessonNo()))
                   .findFirst()
                   .orElse(null);
                if (timetable != null) {
                    timetable.setCourseType(CourseTypeEnum.COURSE_TYPE_ENUM_4.code);
                    timetable.setClassId(makeup.getClassId());
                    timetable.setLessonNo(makeup.getLessonNo());
                    timetable.setLessonOrder(makeup.getLessonOrder());
                    timetable.setCourseId(makeup.getCourseId());
                    timetable.setClassDate(makeup.getClassDate());
                    timetable.setClassStartTime(makeup.getClassStartTime());
                    timetable.setClassEndTime(makeup.getClassEndTime());
                    timetable.setLectureId(makeup.getLectureId());
                }
                });
        }else{
            timetableList = list(
                Wrappers.lambdaQuery(Timetable.class).eq(Timetable::getId, timetableId));
        }
        // 封装其他服务查询数据
        List<TimetableVO> timetableVOList = new ArrayList<>();
        if(!timetableList.isEmpty()){
            timetableVOList = courseMakeUpVoParam(timetableList);
        }
        return timetableVOList.get(0);
    }

    /**
     * 根据课表IDs获取课表详情
     *
     * @param timetableId
     * @return TimetableVO
     */
    @Override
    public List<TimetableVO> getTimetableInfoByIds(List<Long> timetableId, Long storeId) {
        List<Timetable> timetableList = new ArrayList<>();
        List<Timetable> tableList = new ArrayList<>();
        List<Timetable> onlineList = new ArrayList<>();
        List<Long> onlineId = new ArrayList<>();
        List<Long> tableId = new ArrayList<>();
        for (Long id : timetableId) {if (id.toString().length() > 10) {onlineId.add(id);} else {tableId.add(id);}}
        if (ObjectUtil.isNotEmpty(onlineId)) {
            List<BCourseMakeUpOnline> makeupList = bCourseMakeUpOnlineMapper.selectByIdsWithoutDelFlag(onlineId);
            List<String> lessonIds = makeupList.stream()
                .map(e -> e.getLessonNo().toString())
                .distinct()
                .toList();
            List<Timetable> rawOnlineList = this.list(Wrappers.lambdaQuery(Timetable.class)
                .eq(Timetable::getStoreId, storeId)
                .in(Timetable::getLessonNo, lessonIds));
            Map<Long, Timetable> timetableMap = rawOnlineList.stream()
                .collect(Collectors.toMap(Timetable::getLessonNo, Function.identity()));
            for (BCourseMakeUpOnline makeup : makeupList) {
                Timetable original = timetableMap.get(makeup.getLessonNo());
                if(original == null) continue;
                Timetable timetable = new Timetable();
                BeanUtils.copyProperties(original,timetable);
                timetable.setId(makeup.getId());
                timetable.setCourseType(CourseTypeEnum.COURSE_TYPE_ENUM_4.code);
                timetable.setClassId(makeup.getClassId());
                timetable.setLessonNo(makeup.getLessonNo());
                timetable.setLessonOrder(makeup.getLessonOrder());
                timetable.setCourseId(makeup.getCourseId());
                timetable.setClassDate(makeup.getClassDate());
                timetable.setClassStartTime(makeup.getClassStartTime());
                timetable.setClassEndTime(makeup.getClassEndTime());
                timetable.setLectureId(makeup.getLectureId());
                if (ObjectUtil.isNotEmpty(timetable)) {
                    onlineList.add(timetable);
                }
            }
        }
        if(ObjectUtil.isNotEmpty(tableId)){
            tableList = list(Wrappers.lambdaQuery(Timetable.class).in(Timetable::getId, tableId));
        }
        timetableList.addAll(onlineList);
        timetableList.addAll(tableList);
        return courseMakeUpVoParam(timetableList);
    }

    /**
     * 封装其他服务查询数据
     *
     * @param timetableList
     * @return java.util.List<com.yuedu.ydsf.eduConnect.jw.api.vo.TimetableVO>
     * <AUTHOR>
     * @date 2024/12/17 16:44
     */
    private List<TimetableVO> courseMakeUpVoParam(List<Timetable> timetableList) {

        // 直播课-课表
        List<Long> teachingPlanIdList = timetableList.stream()
            .filter(e -> Objects.equals(e.getCourseType(), CourseTypeEnum.COURSE_TYPE_ENUM_1.code))
            .map(Timetable::getTeachingPlanId)
            .distinct()
            .collect(Collectors.toList());

        R<List<TeachingPlanDetailPubVO>> teachingPlanDetailPubVOList = new R<>();
        if (CollectionUtils.isNotEmpty(teachingPlanIdList)) {

            // 查询教学计划明细,声网频道,已结束教学计划版本
            teachingPlanDetailPubVOList = remoteTeachingPlanDetailPubService.getTeachingPlanDetailPubLiveChannel(
                teachingPlanIdList);
            log.debug("直播课频道信息返回参数: {}",
                JSONObject.toJSONString(teachingPlanDetailPubVOList));

        }

        // 点播课-课表
        List<Long> coursePlanIdList2 = timetableList.stream()
            .filter(e -> Objects.equals(e.getCourseType(), CourseTypeEnum.COURSE_TYPE_ENUM_2.code))
            .map(Timetable::getCoursePlanId)
            .distinct()
            .collect(Collectors.toList());

        List<CourseVodPlan> courseVodPlanList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(coursePlanIdList2)) {
            // 查询点播课已结束课版本
            courseVodPlanList = courseVodPlanMapper.selectBatchIds(coursePlanIdList2);
        }

        // 补课-课表
        List<Long> coursePlanIdList3 = timetableList.stream()
            .filter(e -> Objects.equals(e.getCourseType(), CourseTypeEnum.COURSE_TYPE_ENUM_3.code))
            .map(Timetable::getCoursePlanId)
            .distinct()
            .collect(Collectors.toList());

        List<CourseMakeUp> courseMakeUpList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(coursePlanIdList3)) {
            // 查询补课已结束课版本
            courseMakeUpList = courseMakeUpMapper.selectBatchIds(coursePlanIdList3);
        }

        // 封装课表对应上课版本
        List<LessonOrderDTO> lessonOrderDTOList = new ArrayList<>();
        List<CourseVodPlan> finalCourseVodPlanList = courseVodPlanList;
        List<CourseMakeUp> finalCourseMakeUpList = courseMakeUpList;
        R<List<TeachingPlanDetailPubVO>> finalTeachingPlanDetailPubVOList = teachingPlanDetailPubVOList;
        List<TimetableVO> timetableVOList = timetableList.stream().map(entity -> {

            TimetableVO timetableVO = new TimetableVO();
            BeanUtils.copyProperties(entity, timetableVO);

            LessonOrderDTO lessonOrderDTO = new LessonOrderDTO();
            lessonOrderDTO.setCourseId(entity.getCourseId());

            if (CourseTypeEnum.COURSE_TYPE_ENUM_1.code.equals(entity.getCourseType())) {

                // 获取直播课课程版本, 课件版本
                if (finalTeachingPlanDetailPubVOList.isOk() && Objects.nonNull(
                    finalTeachingPlanDetailPubVOList.getData())) {

                    TeachingPlanDetailPubVO teachingPlanDetailPubVO = finalTeachingPlanDetailPubVOList.getData()
                        .stream()
                        .filter(e -> e.getPlanId().equals(entity.getTeachingPlanId())
                            && e.getLessonOrder().equals(entity.getLessonOrder())
                        )
                        .findFirst()
                        .orElse(new TeachingPlanDetailPubVO());

                    lessonOrderDTO.setVersion(teachingPlanDetailPubVO.getCourseVersion());
                    timetableVO.setCourseVersion(teachingPlanDetailPubVO.getCourseVersion());

                }

            } else if (CourseTypeEnum.COURSE_TYPE_ENUM_2.code.equals(entity.getCourseType())) {

                // 获取点播课课程版本, 课件版本
                CourseVodPlan courseVodPlan = finalCourseVodPlanList.stream()
                    .filter(e -> e.getId().equals(entity.getCoursePlanId()))
                    .findFirst()
                    .orElse(new CourseVodPlan());

                lessonOrderDTO.setVersion(courseVodPlan.getCourseVersion());
                timetableVO.setCourseVersion(courseVodPlan.getCourseVersion());
            } else if (CourseTypeEnum.COURSE_TYPE_ENUM_3.code.equals(entity.getCourseType())) {

                // 获取补课课程版本, 课件版本
                CourseMakeUp courseMakeUp = finalCourseMakeUpList.stream()
                    .filter(e -> e.getId().equals(entity.getCoursePlanId()))
                    .findFirst()
                    .orElse(new CourseMakeUp());

                lessonOrderDTO.setVersion(courseMakeUp.getCourseVersion());
                timetableVO.setCourseVersion(courseMakeUp.getCourseVersion());
            }

            lessonOrderDTOList.add(lessonOrderDTO);

            return timetableVO;

        }).toList();

        // 通过课程版本获取课节信息
        log.info("课节信息请求参数{}", JSONObject.toJSONString(lessonOrderDTOList));
        R<List<LessonVO>> lessonList = remoteLessonService.getLessonListByOrder(lessonOrderDTOList);
        log.debug("课节信息返回参数: {}", JSONObject.toJSONString(lessonList));

        // 查询双师阶段信息
        R<List<StageVO>> stageInfoList = remoteStageService.getStageList(
            StageProductEnum.STAGE_PRODUCT_ENUM_1.code);
        log.debug("阶段信息返回参数: {}", JSONObject.toJSONString(stageInfoList));

        // 查询课程信息
        List<Integer> courseIdList = timetableList.stream().map(Timetable::getCourseId)
            .map(Long::intValue).distinct().toList();
        CourseDTO courseDTO = new CourseDTO();
        courseDTO.setCourseIdList(courseIdList);
        R<List<CourseVO>> courseVoList = remoteCourseService.getCourseListByIds(courseDTO);
        log.debug("课程信息返回参数: {}", JSONObject.toJSONString(courseVoList));

        // 查询全部主讲老师
        R<List<LecturerInfoVO>> lecturerInfoList = remoteLecturerInfoService.getAllLecturerInfo();
        log.debug("主讲师信息返回参数: {}", JSONObject.toJSONString(lecturerInfoList));

        // 获取指导老师信息
        List<Long> teacherIdList = timetableList.stream().map(Timetable::getTeacherId).distinct()
            .toList();
        R<List<EmployeeVO>> employeeMapByIdList = remoteEmployeeService.getEmployeeMapByIdList(
            teacherIdList);
        log.debug("指导老师信息返回参数: {}", JSONObject.toJSONString(employeeMapByIdList));

        // 获取班级信息
        List<Long> classIdList = timetableList.stream().map(Timetable::getClassId).distinct()
            .toList();
        R<List<ClassVO>> classByIdList = remoteClassService.getClassByIdList(classIdList);
        log.debug("班级信息返回参数: {}", JSONObject.toJSONString(classByIdList));

        // 获取教室信息
        List<Long> classroomIdList = timetableList.stream().map(Timetable::getClassroomId)
            .distinct().toList();
        R<Map<Long, ClassRoomVO>> roomMapByIdList = remoteClassRoomService.getClassRoomMapByIdList(
            classroomIdList);
        log.debug("教室信息返回参数: {}", JSONObject.toJSONString(roomMapByIdList));

        // 查询时段信息
        R<List<ClassTimeVO>> classTimeVoList = remoteClassTimeService.getClassTimeVoList();
        log.debug("上课时段信息返回参数: {}", JSONObject.toJSONString(classTimeVoList));

        for (TimetableVO timetableVO : timetableVOList) {

            // 课程名称
            if (courseVoList.isOk() && CollectionUtils.isNotEmpty(courseVoList.getData())) {

                CourseVO courseVO = courseVoList.getData().stream()
                    .filter(e -> e.getId().equals(timetableVO.getCourseId().intValue()))
                    .findFirst()
                    .orElse(new CourseVO());

                // 阶段信息
                if (stageInfoList.isOk() && CollectionUtils.isNotEmpty(stageInfoList.getData())) {

                    StageVO stageVO = stageInfoList.getData().stream()
                        .filter(e -> e.getId().equals(courseVO.getStageId()))
                        .findFirst()
                        .orElse(new StageVO());

                    timetableVO.setStageName(stageVO.getStageName());
                }

            }

            // 课节信息
            if (lessonList.isOk() && CollectionUtils.isNotEmpty(lessonList.getData())) {

                List<LessonVO> filteredLessons = lessonList.getData().stream()
                    .filter(e -> e.getCourseId().equals(timetableVO.getCourseId()) &&
                        e.getLessonOrder().equals(timetableVO.getLessonOrder()))
                    .toList();

                LessonVO lessonVO;
                if (Objects.nonNull(timetableVO.getCourseVersion())) {
                    // 如果指定了课程版本，则查找匹配该版本的课节
                    lessonVO = filteredLessons.stream()
                        .filter(e -> Objects.equals(e.getLessonVersion(),
                            timetableVO.getCourseVersion()))
                        .findFirst()
                        .orElseGet(LessonVO::new);

                } else {

                    // 如果没有指定课程版本，则取具有最大/最新版本的元素
                    Optional<LessonVO> maxLesson = filteredLessons.stream()
                        .max(Comparator.comparingInt(LessonVO::getLessonVersion));
                    lessonVO = maxLesson.orElseGet(LessonVO::new);
                }

                timetableVO.setLessonName(lessonVO.getLessonName());
                timetableVO.setImgUrl(lessonVO.getImgUrl());
                timetableVO.setCoursewareId(lessonVO.getCoursewareId());
            }

            // 主讲老师名称
            if (lecturerInfoList.isOk() && Objects.nonNull(lecturerInfoList.getData())) {
                LecturerInfoVO sysUserVO = lecturerInfoList.getData().stream()
                    .filter(e -> e.getUserId().equals(timetableVO.getLectureId()))
                    .findFirst()
                    .orElse(new LecturerInfoVO());

                timetableVO.setLectureName(sysUserVO.getName());
                timetableVO.setLectureNickName(sysUserVO.getNickname());
            }

            // 指导老师名称
            if (employeeMapByIdList.isOk() && Objects.nonNull(employeeMapByIdList.getData())) {

                EmployeeVO employeeVO = employeeMapByIdList.getData().stream()
                    .filter(e -> e.getUserId().equals(timetableVO.getTeacherId()))
                    .findFirst()
                    .orElse(new EmployeeVO());

                timetableVO.setTeacherName(employeeVO.getName());
            }

            // 班级名称
            if (classByIdList.isOk() && Objects.nonNull(classByIdList.getData())) {
                ClassVO classVO = classByIdList.getData().stream()
                    .filter(e -> e.getId().equals(timetableVO.getClassId().intValue()))
                    .findFirst()
                    .orElse(new ClassVO());

                timetableVO.setClassName(classVO.getCName());
            }

            // 教室名称
            if (roomMapByIdList.isOk() && Objects.nonNull(roomMapByIdList.getData())) {
                Map<Long, ClassRoomVO> classRoomInfoData = roomMapByIdList.getData();
                timetableVO.setClassRoomName(
                    Objects.nonNull(classRoomInfoData.get(timetableVO.getClassroomId()))
                        ? classRoomInfoData.get(timetableVO.getClassroomId()).getClassRoomName()
                        : "");
            }

            // 时段
            if (classTimeVoList.isOk() && Objects.nonNull(classTimeVoList.getData())) {
                ClassTimeVO classTimeVO = classTimeVoList.getData().stream()
                    .filter(e -> e.getId().equals(timetableVO.getTimeSlotId()))
                    .findFirst()
                    .orElse(new ClassTimeVO());
                timetableVO.setTimeSlotName(classTimeVO.getName());
            }

            // 直播频道
            if (teachingPlanDetailPubVOList.isOk() && Objects.nonNull(
                teachingPlanDetailPubVOList.getData())) {

                // 直播课-过滤对应的教学计划明细频道
                if (CourseTypeEnum.COURSE_TYPE_ENUM_1.code.equals(timetableVO.getCourseType())) {
                    TeachingPlanDetailPubVO teachingPlanDetailPubVO = teachingPlanDetailPubVOList.getData()
                        .stream()
                        .filter(e -> e.getPlanId().equals(timetableVO.getTeachingPlanId())
                            && e.getLessonOrder().equals(timetableVO.getLessonOrder())
                        )
                        .findFirst()
                        .orElse(new TeachingPlanDetailPubVO());

                    timetableVO.setChannelId(teachingPlanDetailPubVO.getChannelId());
                    timetableVO.setTeachingPlanDetailPubId(teachingPlanDetailPubVO.getId());
                }

            }

            // 上课状态
            timetableVO.setAttendClassState(getAttendClassState(timetableVO));

        }

        return timetableVOList;

    }

    /**
     * 上课状态
     *
     * @param timetableVO
     * @return java.lang.Integer
     * <AUTHOR>
     * @date 2024/12/26 15:35
     */
    private Integer getAttendClassState(TimetableVO timetableVO) {

        Integer state = null;

        LocalDateTime localDateTime = LocalDateTime.now();
        LocalDateTime classStartDateTime = timetableVO.getClassStartDateTime();
        LocalDateTime classEndDateTime = timetableVO.getClassEndDateTime();

        // 距离课程开始时间剩余时间戳
        long currentTime = System.currentTimeMillis();
        Instant instant = classStartDateTime.atZone(ZoneId.systemDefault()).toInstant();
        long attendClassDateStartTimeLong = instant.toEpochMilli();
        long startDiff = (attendClassDateStartTimeLong - currentTime) / 1000 / 60;

        // 未开始(距离开课30分钟之前)
        if (classStartDateTime.isAfter(localDateTime) && startDiff
            > TeachingPlanDetailPubMinuteEnum.TEACHING_PLAN_DETAIL_PUB_MINUTE_ENUM_30.CODE) {
            state = TeachingPlanDetailPubStateEnum.TEACHING_PLAN_DETAIL_PUB_STATE_ENUM_0.CODE;
            return state;
        }

        // 即将开始(距离开课前30分钟内)
        if (classStartDateTime.isAfter(localDateTime) && startDiff
            <= TeachingPlanDetailPubMinuteEnum.TEACHING_PLAN_DETAIL_PUB_MINUTE_ENUM_30.CODE) {
            state = TeachingPlanDetailPubStateEnum.TEACHING_PLAN_DETAIL_PUB_STATE_ENUM_1.CODE;
            return state;
        }

        // 进行中
        if (classStartDateTime.isBefore(localDateTime) && classEndDateTime.isAfter(localDateTime)) {
            state = TeachingPlanDetailPubStateEnum.TEACHING_PLAN_DETAIL_PUB_STATE_ENUM_2.CODE;
            return state;
        }

        // 已结束
        if (classEndDateTime.isBefore(localDateTime)) {
            state = TeachingPlanDetailPubStateEnum.TEACHING_PLAN_DETAIL_PUB_STATE_ENUM_3.CODE;
            return state;
        }

        return state;
    }


    /**
     * 组装课表数据
     *
     * @param addCourseLiveDTO        直播课信息
     * @param roomPlanDetailVersionVO 教学计划详情
     * @param courseLiveId            直播课Id
     * @param courseType              课程类型: 1-直播课; 2-点播课; 3-补课
     */
    @Override
    public Timetable setTimeTable(CourseLiveDTO addCourseLiveDTO,
        LiveRoomPlanDetailVersionVO roomPlanDetailVersionVO, Long courseLiveId, Integer courseType,
        Long storeId, Long lectureIdMain) {
        Timetable timetable = new Timetable();
        timetable.setCoursePlanId(courseLiveId);
        timetable.setCourseType(courseType);
        timetable.setStoreId(storeId);
        if (roomPlanDetailVersionVO != null) {
            timetable.setCourseId(roomPlanDetailVersionVO.getCourseId());
            if (roomPlanDetailVersionVO.getLectureId() == null) {
                timetable.setLectureId(lectureIdMain);
            } else {
                timetable.setLectureId(roomPlanDetailVersionVO.getLectureId());
            }
            timetable.setLessonOrder(roomPlanDetailVersionVO.getLessonOrder());
            timetable.setTimeSlotId(roomPlanDetailVersionVO.getTimeSlotId());
            timetable.setClassDate(roomPlanDetailVersionVO.getClassDate());
            timetable.setClassStartTime(roomPlanDetailVersionVO.getClassStartTime());
            timetable.setClassEndTime(roomPlanDetailVersionVO.getClassEndTime());
            timetable.setClassStartDateTime(roomPlanDetailVersionVO.getClassStartDateTime());
            timetable.setClassEndDateTime(roomPlanDetailVersionVO.getClassEndDateTime());
            timetable.setTeachingPlanId(addCourseLiveDTO.getTeachingPlanId());

            //这节课的编号，课程类型（1位）+ 第几节课（3位）+ 主表的ID
            timetable.setLessonNo(Long.valueOf(
                addCourseLiveDTO.getCourseType() + String.format("%02d",
                    roomPlanDetailVersionVO.getLessonOrder())
                    + courseLiveId));

            //通过上课时段Id查询时段类型
            R<ClassTimeVO> classTimeVoR = remoteClassTimeService.getClassTimeById(
                roomPlanDetailVersionVO.getTimeSlotId());

            if (classTimeVoR != null && classTimeVoR.getCode() == 0) {
                //设置时段类型: 1-上午; 2-下午; 3-晚上;
                timetable.setTimeSlotType(classTimeVoR.getData().getType());
            }
        }
        timetable.setClassroomId(addCourseLiveDTO.getClassroomId());
        timetable.setTeacherId(addCourseLiveDTO.getTeacherId());
        timetable.setClassId(addCourseLiveDTO.getClassId());
        return timetable;
    }

    /**
     * 教学计划主讲老师修改同步更新已约课表(已约直播课/点播课)
     *
     * @return void
     * <AUTHOR>
     * @date 2025/1/13 16:45
     */
    @Override
    @Transactional
    public void editTimetableLecture() {

        log.info("[{}]处理门店预约课表主讲老师开始~~~", LocalDateTime.now());

        // 查询所有未开始的课表(已约直播课/点播课)
        List<Timetable> timetableList = this.list(Wrappers.lambdaQuery(Timetable.class)
            .gt(Timetable::getClassStartDateTime, LocalDateTime.now())
            .in(Timetable::getCourseType, Arrays.asList(CourseTypeEnum.COURSE_TYPE_ENUM_1.code,
                CourseTypeEnum.COURSE_TYPE_ENUM_2.code))
        );

        if (CollectionUtils.isEmpty(timetableList)) {
            log.info("[{}]未开始的课表信息为空", LocalDateTime.now());
            return;
        }

        // 查询教学计划明细
        List<Long> teachingPlanIdIdList = timetableList.stream().map(Timetable::getTeachingPlanId)
            .distinct().toList();
        R<List<TeachingPlanDetailPubVO>> listR = remoteTeachingPlanDetailPubService.getTeachingPlanDetailPubLiveChannel(
            teachingPlanIdIdList);

        if (!listR.isOk() || CollectionUtils.isEmpty(listR.getData())) {
            log.info("[{}]未开始的课表信息对应的教学计划为空", LocalDateTime.now());
            return;
        }

        // 点播课课表
        List<Long> coursePlanIdList2 = timetableList.stream()
            .filter(e -> Objects.equals(e.getCourseType(), CourseTypeEnum.COURSE_TYPE_ENUM_2.code))
            .map(Timetable::getCoursePlanId)
            .distinct()
            .collect(Collectors.toList());

        // 课表对应已约点播课排期
        List<CourseVodPlan> courseVodPlanList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(coursePlanIdList2)) {
            courseVodPlanList = courseVodPlanMapper.selectBatchIds(coursePlanIdList2);
        }

        List<TeachingPlanDetailPubVO> teachingPlanDetailPubVOList = listR.getData();

        for (Timetable timetable : timetableList) {
            try {
                if (CourseTypeEnum.COURSE_TYPE_ENUM_1.code.equals(timetable.getCourseType())) {

                    // 直播课主讲老师更新处理(取教学计划明细表主讲老师)
                    liveLecture(timetable, teachingPlanDetailPubVOList);

                } else if (CourseTypeEnum.COURSE_TYPE_ENUM_2.code.equals(
                    timetable.getCourseType())) {

                    // 点播课主讲老师更新处理(取教学计划表主讲老师)
                    courseVodLecture(timetable, teachingPlanDetailPubVOList, courseVodPlanList);

                }

            } catch (Exception e) {
                log.error("[{}]处理课表主讲老师时发生错误: {}", LocalDateTime.now(),
                    e.getMessage());
            }
        }

        log.info("[{}]处理门店预约课表主讲老师结束~~~", LocalDateTime.now());
    }

    /**
     * 点播课主讲老师更新处理(取教学计划表主讲老师)
     *
     * @param timetable
     * @param teachingPlanDetailPubVOList
     * @return void
     * <AUTHOR>
     * @date 2025/1/14 10:18
     */
    private void courseVodLecture(Timetable timetable,
        List<TeachingPlanDetailPubVO> teachingPlanDetailPubVOList,
        List<CourseVodPlan> courseVodPlanList) {

        // 过滤课表对应教学计划信息
        TeachingPlanDetailPubVO teachingPlanDetailPubVO = teachingPlanDetailPubVOList
            .stream()
            .filter(e -> e.getPlanId().equals(timetable.getTeachingPlanId()))
            .findFirst()
            .orElse(new TeachingPlanDetailPubVO());

        // 教学计划表主讲老师变更才更新
        if (Objects.nonNull(teachingPlanDetailPubVO.getTeachingPlanPubLectureId())
            && !timetable.getLectureId()
            .equals(teachingPlanDetailPubVO.getTeachingPlanPubLectureId())) {

            timetableMapper.update(Wrappers.lambdaUpdate(Timetable.class)
                .eq(Timetable::getId, timetable.getId())
                .set(Timetable::getLectureId, teachingPlanDetailPubVO.getTeachingPlanPubLectureId())
                .set(Timetable::getUpdateTime, LocalDateTime.now())
            );

            // 过滤课表对应已约点播课排期
            CourseVodPlan courseVodPlan = courseVodPlanList
                .stream()
                .filter(e -> e.getId().equals(timetable.getCoursePlanId()))
                .findFirst()
                .orElse(new CourseVodPlan());

            // 更新门店已约点播课表参数封装
            courseVodMapper.update(Wrappers.lambdaUpdate(CourseVod.class)
                .eq(CourseVod::getId, courseVodPlan.getVodCourseId())
                .set(CourseVod::getLectureId, teachingPlanDetailPubVO.getTeachingPlanPubLectureId())
                .set(CourseVod::getUpdateTime, LocalDateTime.now())
            );

        }

    }

    /**
     * 直播课主讲老师更新处理(取教学计划明细表主讲老师)
     *
     * @param timetable
     * @param teachingPlanDetailPubVOList
     * @return void
     * <AUTHOR>
     * @date 2025/1/14 10:14
     */
    private void liveLecture(Timetable timetable,
        List<TeachingPlanDetailPubVO> teachingPlanDetailPubVOList) {

        // 过滤课表对应教学计划明细信息
        TeachingPlanDetailPubVO teachingPlanDetailPubVO = teachingPlanDetailPubVOList
            .stream()
            .filter(e -> e.getPlanId().equals(timetable.getTeachingPlanId())
                && e.getLessonOrder().equals(timetable.getLessonOrder())
            )
            .findFirst()
            .orElse(new TeachingPlanDetailPubVO());

        // 教学计划明细表主讲老师变更才更新
        if (Objects.nonNull(teachingPlanDetailPubVO.getLectureId())
            && !timetable.getLectureId().equals(teachingPlanDetailPubVO.getLectureId())) {
            timetableMapper.update(Wrappers.lambdaUpdate(Timetable.class)
                .eq(Timetable::getId, timetable.getId())
                .set(Timetable::getLectureId, teachingPlanDetailPubVO.getLectureId())
                .set(Timetable::getUpdateTime, LocalDateTime.now())
            );

        }

    }

    /**
     * 查询门店下老师是否存在未结束课程
     *
     * @return true: 存在; false: 不存在
     * <AUTHOR>
     * @date 2025/2/11 11:12
     */
    @Override
    public boolean existNotFinishedByTeacherId(TimetableDTO timetableDTO) {

        // 查询门店下老师是否存在未结束课程
        List<Timetable> timetableList = this.list(Wrappers.lambdaQuery(Timetable.class)
            .eq(Timetable::getStoreId, timetableDTO.getStoreId())
            .eq(Timetable::getTeacherId, timetableDTO.getTeacherId())
            .gt(Timetable::getClassEndDateTime, LocalDateTime.now())
        );

        boolean flag = false;

        if (CollectionUtils.isNotEmpty(timetableList)) {
            flag = true;
        }

        return flag;
    }

    /**
     * 查询班级下是否存在排课课程
     *
     * @param timetableDTO
     * @return true: 存在; false: 不存在
     * <AUTHOR>
     * @date 2025/2/12 15:16
     */
    @Override
    public boolean existScheduleCourseByClassId(TimetableDTO timetableDTO) {

        List<Timetable> timetableList = this.list(Wrappers.lambdaQuery(Timetable.class)
            .eq(Timetable::getClassId, timetableDTO.getClassId())
        );

        boolean flag = false;

        if (CollectionUtils.isNotEmpty(timetableList)) {
            flag = true;
        }

        return flag;
    }

    /**
     * 通过班级获取课程大纲
     *
     * @param classId
     * @return java.util.List<com.yuedu.ydsf.eduConnect.jw.api.vo.TimetableVO>
     * <AUTHOR>
     * @date 2025/2/13 13:52
     */
    @Override
    public IPage<TimetableVO> getTimetableInfoByClassId(Page page, Long classId) {
        log.info("[getTimetableInfoByClassId] 开始获取班级课程大纲, classId: {}", classId);

        try {
            // 查询该班级所有课表信息
            LambdaQueryWrapper<Timetable> queryWrapper =
                Wrappers.lambdaQuery(Timetable.class)
                    .eq(Timetable::getClassId, classId)
                    .orderByAsc(Timetable::getClassStartDateTime);

            // 执行分页查询
            IPage<Timetable> timetablePage = this.page(page, queryWrapper);

            // 如果当前页没有数据，保留总记录数信息
            if (CollectionUtils.isEmpty(timetablePage.getRecords())) {
                log.info(
                    "[getTimetableInfoByClassId] 当前页课表信息为空, classId: {}, current: {}, total: {}",
                    classId,
                    page.getCurrent(),
                    timetablePage.getTotal());
                Page<TimetableVO> emptyPage = new Page<>(page.getCurrent(), page.getSize());
                emptyPage.setTotal(timetablePage.getTotal());
                return emptyPage;
            }
            List<Timetable> timetableList = timetablePage.getRecords();
            // 批量查询课节信息
            List<LessonOrderDTO> lessonOrderDTOList =
                timetableList.stream()
                    .map(
                        timetable -> {
                            LessonOrderDTO lessonOrderDTO = new LessonOrderDTO();
                            lessonOrderDTO.setCourseId(timetable.getCourseId());
                            lessonOrderDTO.setLessonOrderList(
                                Collections.singletonList(timetable.getLessonOrder()));
                            return lessonOrderDTO;
                        })
                    .collect(Collectors.toList());

            R<List<LessonVO>> lessonListResult =
                remoteLessonService.getLessonListByOrder(lessonOrderDTOList);

            if (!lessonListResult.isOk() || CollectionUtils.isEmpty(lessonListResult.getData())) {
                log.error(
                    "[getTimetableInfoByClassId] 获取课节信息失败, classId: {}, error: {}",
                    classId,
                    lessonListResult.getMsg());
                return new Page<>();
            }
            // 转换为VO对象
            List<TimetableVO> timetableVOList =
                timetableList.stream()
                    .map(
                        timetable -> {
                            TimetableVO timetableVO = new TimetableVO();
                            try {
                                // 设置基本信息
                                timetableVO.setId(timetable.getId());
                                timetableVO.setClassStartDateTime(
                                    timetable.getClassStartDateTime());
                                timetableVO.setClassEndDateTime(timetable.getClassEndDateTime());

                                // 格式化完整上课时间
                                String fullClassTime =
                                    String.format(
                                        "%s (%s) %s - %s",
                                        timetable
                                            .getClassStartDateTime()
                                            .format(DateTimeFormatter.ofPattern("yyyy-MM-dd")),
                                        timetable
                                            .getClassStartDateTime()
                                            .getDayOfWeek()
                                            .getDisplayName(TextStyle.SHORT, Locale.CHINESE),
                                        timetable
                                            .getClassStartDateTime()
                                            .format(DateTimeFormatter.ofPattern("HH:mm:ss")),
                                        timetable
                                            .getClassEndDateTime()
                                            .format(DateTimeFormatter.ofPattern("HH:mm:ss")));
                                timetableVO.setFullClassTime(fullClassTime);

                                // 获取对应课节信息
                                LessonVO lessonVO =
                                    lessonListResult.getData().stream()
                                        .filter(
                                            lesson ->
                                                lesson.getCourseId().equals(timetable.getCourseId())
                                                    && lesson
                                                    .getLessonOrder()
                                                    .equals(timetable.getLessonOrder()))
                                        .findFirst()
                                        .orElse(null);

                                if (Objects.nonNull(lessonVO)) {
                                    timetableVO.setLessonName(lessonVO.getLessonName());
                                }

                            } catch (Exception e) {
                                log.error(
                                    "[getTimetableInfoByClassId] 组装课表数据异常, timetable: {}, error: ",
                                    JSONObject.toJSONString(timetable),
                                    e);
                            }
                            return timetableVO;
                        })
                    .collect(Collectors.toList());

            // 构建分页结果
            Page<TimetableVO> resultPage =
                new Page<>(page.getCurrent(), page.getSize(), timetablePage.getTotal());
            resultPage.setRecords(timetableVOList);

            log.info(
                "[getTimetableInfoByClassId] 查询完成, 返回记录数: {}, 总记录数: {}",
                timetableVOList.size(),
                resultPage.getTotal());
            return resultPage;

        } catch (Exception e) {
            log.error("[getTimetableInfoByClassId] 获取班级课程大纲异常, classId: {}, error: ",
                classId, e);
            return new Page<>();
        }
    }

    @Override
    public IPage<TimetableVO> livePageDetails(Page page, Long coursePlanId) {
        return courseLiveManager.fillDetailsData(
            timetableMapper.livePageDetails(page, coursePlanId));
    }

    /**
     * 查询教室是否存在排课课程
     *
     * @param timetableDTO DTO
     * @return Boolean
     */
    @Override
    public Boolean existScheduleCourseByClassRoomId(TimetableDTO timetableDTO) {
        // 检查是否存在
        return this.exists(Wrappers.lambdaQuery(Timetable.class)
            .eq(Timetable::getClassroomId, timetableDTO.getClassroomId()));
    }

    /**
     * 根据id查询排课信息
     *
     * @param timetableIdList DTO
     * @return List<TimetableVO>
     */
    @Override
    public List<TimetableVO> getListById(List<Long> timetableIdList) {
        if (CollUtil.isEmpty(timetableIdList)) {
            return Collections.emptyList();
        }
        log.info("开始查询课表信息, 请求ID列表大小: {}", timetableIdList.size());

        // 区分普通课表ID和线上补课ID (长度大于10位)
        List<Long> normalTimetableIds = new ArrayList<>();
        List<Long> onlineMakeupIds = new ArrayList<>();

        for (Long id : timetableIdList) {
            if (id != null && String.valueOf(id).length() > 10) {
                onlineMakeupIds.add(id);
            } else {
                normalTimetableIds.add(id);
            }
        }

        log.info("ID分类结果 - 普通课表ID数量: {}, 线上补课ID数量: {}",
            normalTimetableIds.size(), onlineMakeupIds.size());

        List<Timetable> timetableList = new ArrayList<>();

        // 查询普通课表
        if (!normalTimetableIds.isEmpty()) {
            log.info("准备查询普通课表信息, ID列表: {}", normalTimetableIds);
            List<Timetable> normalTimetables = this.list(
                Wrappers.lambdaQuery(Timetable.class).in(Timetable::getId, normalTimetableIds));
            log.info("查询到普通课表记录数: {}", normalTimetables.size());
            timetableList.addAll(normalTimetables);
        }

        // 处理线上补课ID - 先查出补课表获取对应的lessonNo再查课表
        if (!onlineMakeupIds.isEmpty()) {
            log.info("检测到线上补课ID, 准备处理线上补课信息, ID列表: {}", onlineMakeupIds);

            try {
                // 查询线上补课表获取lessonNo
                List<BCourseMakeUpOnline> makeupList = bCourseMakeUpOnlineMapper.selectList(
                    Wrappers.lambdaQuery(BCourseMakeUpOnline.class)
                        .in(BCourseMakeUpOnline::getId, onlineMakeupIds));

                log.info("查询到线上补课记录数: {}", makeupList.size());

                if (!makeupList.isEmpty()) {
                    // 收集原课表的lessonNo
                    List<Long> lessonNos = makeupList.stream()
                        .map(BCourseMakeUpOnline::getLessonNo)
                        .distinct()
                        .collect(Collectors.toList());

                    log.info("从线上补课中提取原课表lessonNo: {}", lessonNos);

                    if (!lessonNos.isEmpty()) {
                        // 根据lessonNo查询原课表
                        List<Timetable> makeupTimetables = this.list(
                            Wrappers.lambdaQuery(Timetable.class)
                                .in(Timetable::getLessonNo, lessonNos));

                        log.info("根据lessonNo查询到原课表记录数: {}", makeupTimetables.size());
                        // 添加到结果集
                        timetableList.addAll(makeupTimetables);
                    }
                } else {
                    log.warn("未找到对应的线上补课记录, 线上补课ID列表: {}", onlineMakeupIds);
                }
            } catch (Exception e) {
                log.error("处理线上补课信息异常", e);
            }
        }
//        List<Timetable> timetableList = this.list(
//            Wrappers.lambdaQuery(Timetable.class).in(Timetable::getId, timetableIdList));
        List<LessonOrderDTO> lessonOrderDTOList = new ArrayList<>();

        for (Timetable timetable : timetableList) {
            LessonOrderDTO build = LessonOrderDTO.builder()
                .courseId(timetable.getCourseId())
                .lessonOrderList(Collections.singletonList(timetable.getLessonOrder()))
                .build();
            lessonOrderDTOList.add(build);
        }
        R<List<LessonVO>> lessonListByOrder = remoteLessonService.getLessonListByOrder(
            lessonOrderDTOList);
        if (!lessonListByOrder.isOk()) {
            throw new BizException("远程调用Lesson服务失败");
        }
        List<TimetableVO> timetableVOList = BeanUtil.copyToList(timetableList, TimetableVO.class);
        for (TimetableVO timetable : timetableVOList) {
            Long courseId = timetable.getCourseId();
            Integer lessonOrder = timetable.getLessonOrder();
            for (LessonVO lessonVO : lessonListByOrder.getData()) {
                if (courseId.equals(lessonVO.getCourseId()) && lessonOrder.equals(
                    lessonVO.getLessonOrder())) {
                    timetable.setLessonName(lessonVO.getLessonName());
                }
            }
        }
        return timetableVOList;
    }

    /**
     * 根据id查询排课信息
     *
     * @param ids ids
     * @return List<TimetableClassTimeVO>
     */
    @Override
    public List<TimetableClassTimeVO> getListByIds(List<Long> ids) {

        if (CollUtil.isEmpty(ids)) {
            return Collections.emptyList();
        }

        // 对 ids 进行去重和过滤 null 值处理
        ids = ids.stream().filter(Objects::nonNull).distinct().toList();

        List<Timetable> timetableList = this.list(
            Wrappers.lambdaQuery(Timetable.class).in(Timetable::getId, ids));

        List<TimetableClassTimeVO> timetableClassTimeVOList = new ArrayList<>();
        for (Timetable timetable : timetableList) {
            TimetableClassTimeVO build = TimetableClassTimeVO.builder()
                .id(timetable.getId())
                .fullClassTimeStr(getFullClassTimeStr(timetable))
                .build();
            timetableClassTimeVOList.add(build);
        }

        return timetableClassTimeVOList;
    }

    @Override
    public List<Long> listTimeTableIdByClassDate(TimetableQuery timetableQuery) {
        log.info("根据上课日期查询课次ID,参数: {}", timetableQuery);
        if (Objects.isNull(timetableQuery) || Objects.isNull(
            timetableQuery.getSelectStartClassDate()) ||
            Objects.isNull(timetableQuery.getSelectEndClassDate())) {
            throw new BizException("查询条件不能为空");
        }
        return this.list(Wrappers.<Timetable>lambdaQuery().
                between(Timetable::getClassDate,
                    timetableQuery.getSelectStartClassDate(),
                    timetableQuery.getSelectEndClassDate())
                .eq(Objects.nonNull(timetableQuery.getStoreId()),
                    Timetable::getStoreId, timetableQuery.getStoreId()))
            .stream()
            .map(Timetable::getId)
            .collect(Collectors.toList());
    }

    @Override
    public List<Timetable> listTimetableByClassDate(TimetableQuery timetableQuery) {
        return this.list(Wrappers.<Timetable>lambdaQuery().
            between(Timetable::getClassDate,
                timetableQuery.getSelectStartClassDate(),
                timetableQuery.getSelectEndClassDate())
            .eq(Objects.nonNull(timetableQuery.getStoreId()),
                Timetable::getStoreId, timetableQuery.getStoreId()));
    }

    /**
     * 组装完整上课时间 年月日 时分-时分
     *
     * @param timetable 排课信息
     * @return String
     */
    private String getFullClassTimeStr(Timetable timetable) {
        return String.format(
            "%s %s-%s",
            timetable.getClassStartDateTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")),
            timetable.getClassStartDateTime().format(DateTimeFormatter.ofPattern("HH:mm")),
            timetable.getClassEndDateTime().format(DateTimeFormatter.ofPattern("HH:mm")));
    }

    /**
     * 根据补课id找出对应的原上课的课次相关信息
     * <AUTHOR>
     * @date 2025/4/29 10:13
     * @param makeUpOnLineId
     * @return com.yuedu.ydsf.eduConnect.jw.api.vo.TimetableVO
     */
    @Override
    public TimetableVO getOriginalTimetable(Long makeUpOnLineId) {
        // 参数校验
        if (Objects.isNull(makeUpOnLineId)) {
            throw new BizException("补课ID不能为空");
        }

        try {
            // 1. 查询补课信息
            List<BCourseMakeUpOnline> makeupList = bCourseMakeUpOnlineMapper.selectByIdsWithoutDelFlag(
                Collections.singletonList(makeUpOnLineId));

            if (CollUtil.isEmpty(makeupList)) {
                log.warn("未找到补课记录, makeUpOnLineId: {}", makeUpOnLineId);
                return null;
            }

            // 2. 获取原课次lessonNo
            List<Long> lessonNos = makeupList.stream()
                .map(BCourseMakeUpOnline::getLessonNo)
                .distinct()
                .collect(Collectors.toList());

            log.info("从线上补课中提取原课表lessonNo: {}", lessonNos);

            if (CollUtil.isEmpty(lessonNos)) {
                return null;
            }

            // 3. 查询原课表信息
            List<Timetable> timetables = this.list(
                Wrappers.lambdaQuery(Timetable.class)
                    .in(Timetable::getLessonNo, lessonNos));

            if (CollUtil.isEmpty(timetables)) {
                log.warn("未找到原课表信息, lessonNos: {}", lessonNos);
                return null;
            }

            // 4. 构建课次信息查询参数
            List<LessonOrderDTO> lessonOrderDTOList = timetables.stream()
                .map(timetable -> LessonOrderDTO.builder()
                    .courseId(timetable.getCourseId())
                    .lessonOrderList(Collections.singletonList(timetable.getLessonOrder()))
                    .build())
                .collect(Collectors.toList());

            // 5. 远程调用获取课次详情
            R<List<LessonVO>> lessonResponse = remoteLessonService.getLessonListByOrder(lessonOrderDTOList);
            if (!lessonResponse.isOk() || CollUtil.isEmpty(lessonResponse.getData())) {
                log.error("远程调用Lesson服务失败或返回数据为空, response: {}", lessonResponse);
                throw new BizException("获取课次信息失败");
            }

            // 6. 转换并填充课次信息
            List<TimetableVO> timetableVOList = BeanUtil.copyToList(timetables, TimetableVO.class);
            Map<String, String> lessonNameMap = lessonResponse.getData().stream()
                .collect(Collectors.toMap(
                    lesson -> getLessonKey(lesson.getCourseId(), lesson.getLessonOrder()),
                    LessonVO::getLessonName
                ));

            // 7. 填充课次名称
            timetableVOList.forEach(timetable -> {
                String key = getLessonKey(timetable.getCourseId(), timetable.getLessonOrder());
                timetable.setLessonName(lessonNameMap.get(key));
            });

            return CollUtil.getFirst(timetableVOList);
        } catch (Exception e) {
            log.error("获取原课表信息异常, makeUpOnLineId: {}", makeUpOnLineId, e);
            throw new BizException("获取原课表信息失败");
        }
    }

    /**
     * 生成课次信息的唯一键
     * @param courseId 课程ID
     * @param lessonOrder 课次序号
     * @return 唯一键
     */
    private String getLessonKey(Long courseId, Integer lessonOrder) {
        return courseId + ":" + lessonOrder;
    }

    /**
     * 获取门店下已结束的课程
     * <AUTHOR>
     * @date 2025/5/7 15:19
     * @param page 分页参数
     * @param timetableQuery 查询条件
     * @return com.baomidou.mybatisplus.core.metadata.IPage<com.yuedu.ydsf.eduConnect.jw.api.vo.TimetableVO>
     */
    @Override
    public IPage<TimetableVO> getFinishTimetable(Page page, TimetableQuery timetableQuery) {
        log.info("开始获取门店已结束课程列表, 查询参数: {}", JSONObject.toJSONString(timetableQuery));
        Long storeId = StoreContextHolder.getStoreId();

        // 构建基础查询条件
        LambdaQueryWrapper<Timetable> queryWrapper =
            Wrappers.lambdaQuery(Timetable.class)
                .in(
                    Timetable::getCourseType,
                    Arrays.asList(
                        CourseTypeEnum.COURSE_TYPE_ENUM_1.code, CourseTypeEnum.COURSE_TYPE_ENUM_2.code))
                .eq(
                    Objects.nonNull(timetableQuery.getCourseId()),
                    Timetable::getCourseId,
                    timetableQuery.getCourseId())
                .eq(Timetable::getStoreId, storeId)
                .lt(Timetable::getClassEndDateTime, LocalDateTime.now())
                .orderByDesc(Timetable::getClassStartDateTime, Timetable::getId);

        // 如果有课节名称搜索条件，先获取符合条件的课节信息
        if (StringUtils.isNotBlank(timetableQuery.getLessonName())) {
            CoursePublishQuery coursePublishQuery = new CoursePublishQuery();
            coursePublishQuery.setLessonName(timetableQuery.getLessonName());
            // 调用远程服务获取符合课节名称条件的课节信息
            R<List<LessonEntity>> lessonResult = remoteLessonService.getLessonByName(coursePublishQuery);

            if (!lessonResult.isOk() || CollectionUtils.isEmpty(lessonResult.getData())) {
                log.info("未找到匹配课节名称[{}]的课节信息", timetableQuery.getLessonName());
                return new Page<>(page.getCurrent(), page.getSize());
            }

            // 提取课程ID和课节序号对，用于构建查询条件
            List<LessonEntity> matchedLessons = lessonResult.getData();
            log.info("找到匹配课节名称[{}]的课节数量: {}", timetableQuery.getLessonName(), matchedLessons.size());

            // 由于可能有多个匹配的课节，我们需要使用OR条件
            if (!matchedLessons.isEmpty()) {
                queryWrapper.and(wrapper -> {
                    for (LessonEntity lesson : matchedLessons) {
                        wrapper.or(w -> w
                            .eq(Timetable::getCourseId, lesson.getCourseId())
                            .eq(Timetable::getLessonOrder, lesson.getLessonOrder())
                        );
                    }
                });
            }
        }

        // 执行分页查询
        Page<Timetable> timetablePage = this.page(page, queryWrapper);

        if (CollectionUtils.isEmpty(timetablePage.getRecords())) {
            log.info("未查询到满足条件的已结束课程数据");
            return new Page<>(page.getCurrent(), page.getSize());
        }

        // 获取课节信息、班级、教师信息等
        List<Timetable> timetableList = timetablePage.getRecords();
        List<TimetableVO> timetableVOList = courseMakeUpVoParam(timetableList);

        // 获取所有涉及的课次号
        List<Long> lessonNoList = timetableList.stream()
            .map(Timetable::getLessonNo)
            .distinct()
            .toList();

        // 获取考勤统计信息
        Map<Long, AttendanceManagementVO> attendanceMap = getAttendanceStatistics(lessonNoList, storeId);

        // 填充考勤数据到课表列表
        for (TimetableVO timetableVO : timetableVOList) {
            AttendanceManagementVO attendanceVO = attendanceMap.get(timetableVO.getLessonNo());
            if (attendanceVO != null) {
                // 拆分为两个独立字段
                timetableVO.setCheckedInCount(attendanceVO.getCheckedInCount());
                timetableVO.setTotalCount(attendanceVO.getTotalCount());
                // 保留原有格式化字符串
                timetableVO.setStudentAttendanceCount(String.format("%d/%d",
                    attendanceVO.getCheckedInCount(),
                    attendanceVO.getTotalCount()));
            } else {
                timetableVO.setCheckedInCount(0);
                timetableVO.setTotalCount(0);
                timetableVO.setStudentAttendanceCount("0/0");
            }

            // 格式化完整上课时间为 "2024-11-1(周一)08:00-10:00" 格式
            String fullClassTime = String.format("%s(%s) %s-%s",
                timetableVO.getClassStartDateTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")),
                getDayOfWeekChinese(timetableVO.getClassStartDateTime().getDayOfWeek()),
                timetableVO.getClassStartDateTime().format(DateTimeFormatter.ofPattern("HH:mm")),
                timetableVO.getClassEndDateTime().format(DateTimeFormatter.ofPattern("HH:mm")));
            timetableVO.setDateTimeSlot(fullClassTime);
        }

        // 构建分页结果
        IPage<TimetableVO> resultPage = new Page<>(page.getCurrent(), page.getSize(), timetablePage.getTotal());
        resultPage.setRecords(timetableVOList);

        log.info("获取门店已结束课程列表完成, 总记录数: {}, 当前页记录数: {}",
            resultPage.getTotal(), timetableVOList.size());
        return resultPage;
    }



    /**
     * 获取课次的考勤统计信息
     *
     * @param lessonNoList 课次编号列表
     * @param storeId 门店ID
     * @return 课次考勤统计信息Map
     */
    private Map<Long, AttendanceManagementVO> getAttendanceStatistics(List<Long> lessonNoList, Long storeId) {
        if (CollectionUtils.isEmpty(lessonNoList)) {
            return Collections.emptyMap();
        }

        log.info("开始查询课次考勤统计信息, lessonNoList: {}, storeId: {}", lessonNoList, storeId);

        // 1. 查询课次对应的所有学生记录
        List<BClassTimeStudent> allStudents = bClassTimeStudentMapper.selectList(
            Wrappers.lambdaQuery(BClassTimeStudent.class)
                .in(BClassTimeStudent::getLessonNo, lessonNoList)
                .eq(Objects.nonNull(storeId), BClassTimeStudent::getStoreId, storeId)
                .eq(BClassTimeStudent::getAdjustStatus, Integer.valueOf(YesNoEnum.NO.getCode())) // 未调出
        );

        if (CollectionUtils.isEmpty(allStudents)) {
            log.info("未查询到课次学生记录");
            return Collections.emptyMap();
        }

        // 2. 统计每个课次的学生总数和已签到学生数
        Map<Long, List<BClassTimeStudent>> studentsByLessonNo = allStudents.stream()
            .collect(Collectors.groupingBy(BClassTimeStudent::getLessonNo));

        // 3. 构建考勤统计结果
        Map<Long, AttendanceManagementVO> result = new HashMap<>(lessonNoList.size());

        for (Long lessonNo : lessonNoList) {
            List<BClassTimeStudent> students = studentsByLessonNo.getOrDefault(lessonNo, Collections.emptyList());

            // 计算已签到人数
            long checkedInCount = students.stream()
                .filter(student -> Objects.equals(student.getCheckInStatus(), CheckInStatusEnum.CHECK_IN_STATUS_1.code))
                .count();

            // 计算总人数(所有未调出的学生)
            long totalCount = students.size();

            // 创建考勤统计对象
            AttendanceManagementVO vo = new AttendanceManagementVO();
            vo.setLessonNo(lessonNo);
            vo.setCheckedInCount((int) checkedInCount);
            vo.setTotalCount((int) totalCount);

            result.put(lessonNo, vo);
        }

        log.info("课次考勤统计信息查询完成, 统计结果: {}", JSON.toJSONString(result));
        return result;
    }

    /**
     * 获取中文星期几
     * @param dayOfWeek 星期几枚举
     * @return 中文星期几（周一、周二等）
     */
    private String getDayOfWeekChinese(DayOfWeek dayOfWeek) {
        switch (dayOfWeek) {
            case MONDAY: return "周一";
            case TUESDAY: return "周二";
            case WEDNESDAY: return "周三";
            case THURSDAY: return "周四";
            case FRIDAY: return "周五";
            case SATURDAY: return "周六";
            case SUNDAY: return "周日";
            default: return "";
        }
    }


    @Override
    public List<Long> getCourseListByStoreId(Long storeId) {
        List<Timetable> courseListByStoreId = baseMapper.getCourseListByStoreId(storeId);

        if(CollectionUtils.isEmpty(courseListByStoreId)){
            return Collections.emptyList();
        }

      return courseListByStoreId
            .stream()
            .map(Timetable::getCourseId)
            .distinct()
            .toList();
    }
}
