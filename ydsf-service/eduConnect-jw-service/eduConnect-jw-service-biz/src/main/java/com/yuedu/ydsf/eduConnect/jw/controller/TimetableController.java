package com.yuedu.ydsf.eduConnect.jw.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yuedu.ydsf.common.core.util.R;
import com.yuedu.ydsf.common.feign.annotation.NoToken;
import com.yuedu.ydsf.common.security.annotation.HasPermission;
import com.yuedu.ydsf.common.security.annotation.Inner;
import com.yuedu.ydsf.common.security.annotation.StorePermission;
import com.yuedu.ydsf.common.security.util.StoreContextHolder;
import com.yuedu.ydsf.eduConnect.jw.api.dto.TimetableDTO;
import com.yuedu.ydsf.eduConnect.jw.api.query.TimetableQuery;
import com.yuedu.ydsf.eduConnect.jw.api.valid.TimetableValidGroup;
import com.yuedu.ydsf.eduConnect.jw.api.vo.BClassTimeStudentVO;
import com.yuedu.ydsf.eduConnect.jw.api.vo.TimeTypeTimetableVO;
import com.yuedu.ydsf.eduConnect.jw.api.vo.TimetableClassTimeVO;
import com.yuedu.ydsf.eduConnect.jw.api.vo.TimetableVO;
import com.yuedu.ydsf.eduConnect.jw.service.BClassTimeStudentService;
import com.yuedu.ydsf.eduConnect.jw.service.TimetableService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import java.time.LocalDate;
import java.util.List;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 门店课表 控制类
 *
 * <AUTHOR>
 * @date 2024-12-09 10:27:50
 */
@Validated
@RestController
@RequiredArgsConstructor
@RequestMapping("/timetable")
@Tag(description = "b_timetable", name = "门店课表管理")
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class TimetableController {

    private final TimetableService timetableService;

    private final BClassTimeStudentService classTimeStudentService;

    /**
     * 获取课程时间表
     *
     * @param timetableQuery 查询条件
     * @return List<LocalDate>
     */
    @Operation(summary = "获取课程时间表", description = "获取课程时间表")
    @GetMapping("/getTimetableClassDateList")
    @StorePermission
    public R<List<LocalDate>> getTimetableClassDateList(@Valid @ParameterObject TimetableQuery timetableQuery) {
        timetableQuery.setStoreId(StoreContextHolder.getStoreId());
        return R.ok(timetableService.getTimetableClassDateList(timetableQuery));
    }

    /**
     * 根据日期获取课表列表
     *
     * @param timetableDTO 日期
     * @return List<TimeTypeTimetableVO>
     */
    @Operation(summary = "根据日期获取课表列表", description = "根据日期获取课表列表")
    @GetMapping("/getTimeTableByDate")
    @StorePermission
    public R<List<TimeTypeTimetableVO>> getTimeTableByDate(
        @Validated(TimetableValidGroup.GetTimeTableByDateGroup.class) @ParameterObject
        TimetableDTO timetableDTO) {
        timetableDTO.setStoreId(StoreContextHolder.getStoreId());
        return R.ok(timetableService.getTimeTableByDate(timetableDTO));
    }

    /**
     * 根据课表ID获取课表详情
     *
     * @param timetableId
     * @return TimetableVO
     */
    @Operation(summary = "根据课表ID获取课表详情", description = "根据课表ID获取课表详情")
    @GetMapping("/getTimetableInfoById/{timetableId}")
    @StorePermission
    public R<TimetableVO> getTimetableInfoById(
        @PathVariable("timetableId") @NotNull(message = "课表ID不能为空")
        @Parameter(description = "课表ID") Long timetableId) {
        return R.ok(timetableService.getTimetableInfoById(timetableId));
    }

    /**
     * 查询门店下老师是否存在未结束课程
     * @return true: 存在; false: 不存在
     * <AUTHOR>
     * @date 2025/2/11 11:12
     */
    @Operation(summary = "查询门店下老师是否存在未结束课程",description = "查询门店下老师是否存在未结束课程")
    @PostMapping("/existNotFinishedByTeacherId")
    @Inner
    public R<Boolean> existNotFinishedByTeacherId(@Validated(TimetableValidGroup.ExistNotFinishedByTeacherIdGroup.class) @RequestBody TimetableDTO timetableDTO){
        return R.ok(timetableService.existNotFinishedByTeacherId(timetableDTO));
    }

    /**
     * 查询班级下是否存在排课
     * @param timetableDTO
     * @return com.yuedu.ydsf.common.core.util.R<java.lang.Boolean>
     * <AUTHOR>
     * @date 2025/2/12 15:14
     */
    @Operation(summary = "查询班级下是否存在排课课程",description = "查询班级下是否存在排课课程")
    @PostMapping("/existScheduleCourseByClassId")
    @Inner
    public R<Boolean> existScheduleCourseByClassId(@Validated(TimetableValidGroup.ExistScheduleCourseByClassIdGroup.class) @RequestBody TimetableDTO timetableDTO){
        return R.ok(timetableService.existScheduleCourseByClassId(timetableDTO));
    }

  /**
   * 通过班级获取课程大纲
   *
   * <AUTHOR>
   * @date 2025/2/13 13:51
   * @param classId
   * @return com.yuedu.ydsf.common.core.util.R<com.yuedu.ydsf.eduConnect.jw.api.vo.TimetableVO>
   */
  @Operation(summary = "通过班级获取课程大纲", description = "通过班级获取课程大纲")
  @GetMapping("/getTimetableInfoByClassId/{classId}")
  @StorePermission
  public R<IPage<TimetableVO>> getTimetableInfoByClassId(
      @ParameterObject Page page,
      @PathVariable("classId") @NotNull(message = "班级ID不能为空") @Parameter(description = "班级ID")
          Long classId) {
    IPage<TimetableVO> result = timetableService.getTimetableInfoByClassId(page, classId);
    return R.ok(result);
  }

  /**
   * 获取门店下已结束的课程
   * <AUTHOR>
   * @date 2025/5/7 15:18
   * @param page
   * @param timetableQuery
   * @return com.yuedu.ydsf.common.core.util.R<com.baomidou.mybatisplus.core.metadata.IPage<com.yuedu.ydsf.eduConnect.jw.api.vo.TimetableVO>>
   */
    @Operation(summary = "获取门店下已结束的课程", description = "获取门店下已结束的课程")
    @GetMapping("/getFinishTimetable")
    @StorePermission
    public R<IPage<TimetableVO>> getFinishTimetable(
        @ParameterObject Page page,
        @ParameterObject TimetableQuery timetableQuery) {
        IPage<TimetableVO> result = timetableService.getFinishTimetable(page, timetableQuery);
        return R.ok(result);
    }

    /**
     *  直播约课分页明细
     *
     * <AUTHOR>
     * @date 2025年02月25日 19时36分
     */
    @Operation(summary = "直播约课分页明细" , description = "直播约课分页明细" )
    @GetMapping("/live/details" )
    @HasPermission("edusystem_dourseLive_details")
    public R<IPage<TimetableVO>> courseLiveDetails(@ParameterObject Page page,@ParameterObject TimetableQuery timetableQuery) {
        if(Objects.isNull(timetableQuery.getCoursePlanId())){
            return R.failed("直播约课ID不能为空");
        }
        return R.ok(timetableService.livePageDetails(page, timetableQuery.getCoursePlanId()));
    }

    /**
     * 查询教室是否存在排课课程
     * @param timetableDTO DTO
     * @return Boolean
     */
    @Operation(summary = "查询教室下是否存在排课课程", description = "查询教室下是否存在排课课程")
    @PostMapping("/existScheduleCourseByClassRoomId")
    @Inner
    R<Boolean> existScheduleCourseByClassRoomId(@RequestBody TimetableDTO timetableDTO){
        return R.ok(timetableService.existScheduleCourseByClassRoomId(timetableDTO));
    }

    /**
     * 根据ID获取排课列表
     * @param timetableDTO DTO
     * @return List<TimetableVO>
     */
    @PostMapping("/getListById")
    @Inner
    public R<List<TimetableVO>> getListById(@RequestBody TimetableDTO timetableDTO){
        return R.ok(timetableService.getListById(timetableDTO.getIdList()));
    }

    /**
     * 根据ID获取课表
     * @param ids DTO
     * @return List<TimetableClassTimeVO>
     */
    @PostMapping("/getListByIds")
    @Inner
    public R<List<TimetableClassTimeVO>> getListByIds(@RequestBody List<Long> ids){
        return R.ok(timetableService.getListByIds(ids));
    }


    /**
     *
     *  根据课号查询对应学生列表
     *
     * <AUTHOR>
     * @date 2025年03月11日 16时26分
     */
    @PostMapping("/getClassTimeStudentListByIds")
    @Inner
    public R<List<BClassTimeStudentVO>> getBClassTimeStudentList(@RequestBody List<Long> ids){
        return R.ok(classTimeStudentService.getBClassTimeStudentList(ids));
    }

    /**
     * 根据上课日期查询课次ID
     */
    @PostMapping("/listTimeTableIdByClassDate")
    @Inner
    public R<List<Long>> listTimeTableIdByClassDate(@RequestBody TimetableQuery timetableQuery) {
        return R.ok(timetableService.listTimeTableIdByClassDate(timetableQuery));
    }

    /**
     * 根据补课id找出对应的原上课的课次相关信息
     */
    @GetMapping("/getOriginalTimetable/{makeUpOnLineId}")
    @Inner
    public TimetableVO getOriginalTimetable(@PathVariable("makeUpOnLineId") Long makeUpOnLineId) {
        return timetableService.getOriginalTimetable(makeUpOnLineId);
    }


    /**
     *  根据门店ID查询已约课程ID列表
     *
     * <AUTHOR>
     * @date 2025年06月05日 16时28分
     */
    @GetMapping("/getCourseListByStoreId/{storeId}")
    @Inner
    R<List<Long>> getCourseListByStoreId(Long storeId){
        return R.ok(timetableService.getCourseListByStoreId(storeId));
    }

    /**
     * 根据课表ID获取课表详情(内部)
     *
     * @param timetableId
     * @return TimetableVO
     */
    @Operation(summary = "根据课表ID获取课表详情", description = "根据课表ID获取课表详情")
    @GetMapping("/getTimetableInfoByTimetableId/{timetableId}")
    @Inner
    public R<TimetableVO> getTimetableInfoByTimetableId(
        @PathVariable("timetableId") @NotNull(message = "课表ID不能为空")
        @Parameter(description = "课表ID") Long timetableId) {
        return R.ok(timetableService.getTimetableInfoById(timetableId));
    }

    /**
     *
     *  根据课表IDs获取课表列表
     *
     * <AUTHOR>
     * @date 2025年06月11日 15时26分
     */
    @PostMapping("/getTimetableInfoByTimetableIds")
    @Inner
    public R<List<TimetableVO>> getTimetableInfoByTimetableIds(@RequestBody List<Long> ids,Long storeId){
        return R.ok(timetableService.getTimetableInfoByIds(ids, storeId));
    }

}
