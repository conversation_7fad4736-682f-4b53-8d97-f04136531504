package com.yuedu.teaching.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yuedu.teaching.dto.CoursewareStepTemplateAddDTO;
import com.yuedu.teaching.entity.CoursewareStepTemplate;
import com.yuedu.teaching.vo.CoursewareStepTemplateVO;
import com.yuedu.teaching.vo.StepVO;

import java.util.List;

/**
 * 教学环节模版表 服务类
 *
 * <AUTHOR>
 * @date 2024-11-05 17:32:03
 */
public interface CoursewareStepTemplateService extends IService<CoursewareStepTemplate> {
    /**
     * 新增教学环节模板
     *
     * @param coursewareStepTemplateAddDTO 新增参数
     * @return 新增结果
     */
    Boolean addCoursewareStepTemplate(CoursewareStepTemplateAddDTO coursewareStepTemplateAddDTO);

    /**
     * 查询教学环节模板
     *
     * @return 查询结果
     */
    List<CoursewareStepTemplateVO> listCoursewareStepTemplate();

    /**
     * 根据id查询教学环节模板
     *
     * @param templateId 查询参数模版ID
     * @return 查询结果
     */
    List<StepVO> getCoursewareStepByIdTemplate(Integer templateId);
}
