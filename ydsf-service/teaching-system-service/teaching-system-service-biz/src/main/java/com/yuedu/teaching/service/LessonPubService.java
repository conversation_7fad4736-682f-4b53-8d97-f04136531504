package com.yuedu.teaching.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yuedu.teaching.entity.LessonPubEntity;
import com.yuedu.teaching.query.LessonPubQuery;

import java.util.List;
import java.util.Map;

public interface LessonPubService extends IService<LessonPubEntity> {

    /**
     * 通过课程Id,课节顺序查询课节信息
     *
     * @return 结果
     */
    List<LessonPubEntity> lessonPubQueryList(LessonPubQuery lessonPubQuery);

    Map<Long, List<LessonPubEntity>> lessonPubQueryMap(List<Long> courseIdList);
}