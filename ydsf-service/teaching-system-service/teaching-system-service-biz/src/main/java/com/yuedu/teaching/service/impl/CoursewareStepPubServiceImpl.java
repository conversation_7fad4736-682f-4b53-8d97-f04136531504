package com.yuedu.teaching.service.impl;


import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.houbb.heaven.util.lang.BeanUtil;
import com.yuedu.teaching.constant.TeachingConstant;
import com.yuedu.teaching.entity.*;
import com.yuedu.teaching.mapper.*;
import com.yuedu.teaching.query.CoursewareStepQuery;
import com.yuedu.teaching.service.CoursewarePubService;
import com.yuedu.teaching.service.CoursewareStepPubService;
import com.yuedu.teaching.vo.ClientStepDetailsVO;
import com.yuedu.teaching.vo.PictureBookRolePcVO;
import com.yuedu.teaching.vo.StepPubVO;
import com.yuedu.ydsf.common.data.resolver.ParamResolver;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

import static com.yuedu.teaching.service.impl.CoursewareDataStepDetailsServiceImpl.addPathField;

/**
 * 教学环节版本表 服务类
 *
 * <AUTHOR>
 * @date 2024-11-06 08:43:33
 */
@Slf4j
@Service
@AllArgsConstructor
public class CoursewareStepPubServiceImpl extends ServiceImpl<CoursewareStepPubMapper, CoursewareStepPub> implements CoursewareStepPubService {

    private final CoursewareMapper coursewareMapper;

    private final CoursewareStepMapper coursewareStepMapper;

    private final CoursewareStepPubMapper coursewareStepPubMapper;

    private final CoursewareDataStepDetailsPubMapper coursewareDataStepDetailsPubMapper;

    private final TeachingPageTemplateMapper teachingPageTemplateMapper;

    private final CoursewarePubService coursewarePubService;

    /**
     * 发布step表
     *
     * @param data                 coursewareData实体类
     * @param coursewareDataIdList data表id集合
     * @return List<CoursewareStep>
     */
    @Override
    public List<CoursewareStep> pubCoursewareStep(CoursewareData data, List<Integer> coursewareDataIdList, Integer coursewareVersionId) {
        log.info("发布CoursewareStep表:{}", data);
        List<CoursewareStep> coursewareStepList = coursewareStepMapper.selectList(Wrappers.lambdaQuery(CoursewareStep.class)
                .in(CoursewareStep::getCoursewareDataId, coursewareDataIdList)
                .eq(CoursewareStep::getCoursewareId, data.getCoursewareId()));

        List<CoursewareStepPub> coursewareStepPubList = coursewareStepList.stream()
                .map(coursewareStep -> {
                    CoursewareStepPub coursewareDataPub = new CoursewareStepPub();
                    BeanUtil.copyProperties(coursewareStep, coursewareDataPub);
                    coursewareDataPub.setVersion(coursewareVersionId);
                    coursewareDataPub.setStepId(coursewareStep.getId());
                    coursewareDataPub.setId(null);
                    return coursewareDataPub;
                })
                .toList();
        coursewareStepPubMapper.insert(coursewareStepPubList);
        return coursewareStepList;
    }


    @Override
    public List<ClientStepDetailsVO> viewCoursewareStep(CoursewareStepQuery coursewareStepQuery) {
        Integer coursewareId = coursewareStepQuery.getCoursewareId();
        Integer coursewareDataId = coursewareStepQuery.getCoursewareDataId();

        //获取课件版本
        Courseware courseware = coursewareMapper.selectById(coursewareId);
        if (ObjectUtil.isNull(courseware)) {
            return Collections.emptyList();
        }
        if (courseware.getVersion() == null || courseware.getVersion() == 0) {
            return Collections.emptyList();
        }

        // 获取课节环节详情
        List<StepPubVO> coursewareStepPub = getStepDetails(coursewareId, coursewareDataId, courseware.getVersion());

        // 将 StepPubVO 转换为 ClientStepDetailsVO
        if (coursewareStepPub == null || coursewareStepPub.isEmpty()) {
            return Collections.emptyList();
        }

        ClientStepDetailsVO stepDetailsVo = new ClientStepDetailsVO();
        stepDetailsVo.setPages(coursewareStepPub);

        // 返回包含单个对象的列表（根据需求调整）
        return Collections.singletonList(stepDetailsVo);
    }


    public List<StepPubVO> getStepDetails(Integer coursewareId, Integer coursewareDataId,Integer version) {

        // 获取课节环节
        List<CoursewareStepPub> steps = getCoursewareStepList(coursewareId, coursewareDataId,version);
        if (Objects.isNull(steps)) {
            return Collections.emptyList();
        }
        // 获取steps 的ids列表
        List<Integer> stepIds = steps.stream().map(CoursewareStepPub::getStepId).toList();

        // 获取并分组课件步骤详情
        Map<Integer, CoursewareDataStepDetailsPub> stepDetailsMap = getStepDetailsMap(coursewareId, coursewareDataId, version, stepIds);

        Map<Integer, CoursewareStepPub> stepMap = steps.stream().collect(Collectors.toMap(CoursewareStepPub::getStepId, step -> step));
        // 获取所有模版
        Map<Integer, String> templateMap = getTemplate();

        return steps.stream()
                .filter(step -> step.getType() == TeachingConstant.COURSEWARE_STEP_TYPE_PAGE)
                .filter(step -> ObjectUtil.isNotNull(stepDetailsMap.get(step.getStepId())))
                .map(step -> {
                    StepPubVO stepViewVO = new StepPubVO();
                    stepViewVO.setId(step.getStepId());
                    stepViewVO.setStepOrder(step.getStepOrder());
                    stepViewVO.setPageTemplateId(step.getPageTemplateId());
                    stepViewVO.setViewUrl(templateMap.get(step.getPageTemplateId()));
                    stepViewVO.setStepParentOrder(stepMap.get(step.getStepParent()).getStepOrder());

                    CoursewareDataStepDetailsPub stepDetails = stepDetailsMap.get(step.getStepId());
                    JSONObject objectDetails = JSONUtil.parseObj(ObjectUtil.defaultIfNull(stepDetails.getDetails(), "{}"));
                    JSONObject objectTool = JSONUtil.parseObj(ObjectUtil.defaultIfNull(stepDetails.getTool(), "{}"));

                    if (Objects.equals(step.getPageTemplateId(), getConfigPictureBookTemplateId())) {
                        // 根据绘本查询角色数据
                        List<PictureBookRolePcVO> roleList = coursewarePubService.getRoleList(coursewareId);
                        if (roleList == null || roleList.isEmpty()) {
                            roleList = new ArrayList<>();
                        }
                        objectDetails.set("role", roleList);
                    }

                    objectDetails.set("stepName", stepMap.get(step.getStepParent()).getStepName());

                    // 教学文案
                    String teachingPlan = "";
                    if (Objects.nonNull(stepDetailsMap.get(step.getStepParent()))) {
                        // 获取details
                        teachingPlan = JSONUtil.parseObj(ObjectUtil.defaultIfNull(stepDetailsMap.get(step.getStepParent()).getDetails(), "{}")).getStr("teachingPlan", "");
                    }
                    objectDetails.set("teachingPlan", teachingPlan);
                    addPathField(objectDetails);
                    stepViewVO.setConfigs(objectDetails);
                    addPathField(objectTool);
                    stepViewVO.setTools(objectTool);
                    return stepViewVO;
                })
                .sorted(Comparator.comparingInt(StepPubVO::getStepParentOrder)
                        .thenComparingInt(StepPubVO::getStepOrder))
                .toList();
    }

    /**
     * 获取所有教学环节
     *
     * @param coursewareId     课件ID
     * @param coursewareDataId 课件内容ID
     * @return 教学环节列表
     */
    public List<CoursewareStepPub> getCoursewareStepList(Integer coursewareId, Integer coursewareDataId,Integer version) {
        // 获取教学环节数据
        return this.list(Wrappers.<CoursewareStepPub>lambdaQuery()
                .eq(CoursewareStepPub::getCoursewareId, coursewareId)
                .eq(CoursewareStepPub::getCoursewareDataId, coursewareDataId)
                .eq(CoursewareStepPub::getVersion, version)
                .orderByAsc(CoursewareStepPub::getStepOrder)
                .orderByDesc(CoursewareStepPub::getCreateTime)
        );
    }

    /**
     * 获取所有模版
     *
     * @return Map<Integer, String>
     */
    private Map<Integer, String> getTemplate() {
        return teachingPageTemplateMapper.selectList(Wrappers.lambdaQuery(TeachingPageTemplateEntity.class)).stream()
                .collect(Collectors.toMap(
                        TeachingPageTemplateEntity::getId,
                        TeachingPageTemplateEntity::getViewUrl
                ));
    }

    /**
     * 获取并分组课件步骤详情
     *
     * @return Map<Integer, CoursewareDataStepDetailsPub>
     */
    private Map<Integer, CoursewareDataStepDetailsPub> getStepDetailsMap(Integer coursewareId, Integer coursewareDataId,Integer version, List<Integer> stepIds) {
        // 获取并分组课件步骤详情
        return coursewareDataStepDetailsPubMapper.selectList(Wrappers.<CoursewareDataStepDetailsPub>lambdaQuery()
                        .eq(CoursewareDataStepDetailsPub::getCoursewareId, coursewareId)
                        .eq(CoursewareDataStepDetailsPub::getCoursewareDataId, coursewareDataId)
                        .eq(CoursewareDataStepDetailsPub::getVersion, version)
                        .in(CoursewareDataStepDetailsPub::getStepId, stepIds))
                .stream()
                .collect(Collectors.toMap(
                        CoursewareDataStepDetailsPub::getStepId,
                        coursewareDataStepDetailsPub -> coursewareDataStepDetailsPub
                ));
    }

    /**
     * 获取模版配置ID
     *
     * @return Integer
     */
    private Integer getConfigPictureBookTemplateId() {
        String pictureBookTemplateId = ParamResolver.getStr(TeachingConstant.PICTURE_BOOK_TEMPLATE_ID);
        if (pictureBookTemplateId == null || pictureBookTemplateId.trim().isEmpty()) {
            return 0;
        }
        return Integer.valueOf(pictureBookTemplateId);
    }
}
