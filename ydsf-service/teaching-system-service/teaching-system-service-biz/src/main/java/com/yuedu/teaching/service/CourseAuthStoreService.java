package com.yuedu.teaching.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yuedu.teaching.query.CourseAuthStoreQuery;
import com.yuedu.teaching.dto.CourseAuthStoreDTO;
import com.yuedu.teaching.vo.CourseAuthStoreVO;
import com.yuedu.teaching.entity.CourseAuthStore;

import java.util.List;

/**
* 课程授权门店表服务接口
*
* <AUTHOR>
* @date  2025/05/21
*/
public interface CourseAuthStoreService extends IService<CourseAuthStore> {



    /**
    * 课程授权门店表分页查询
    * @param page 分页对象
    * @param courseAuthStoreQuery 课程授权门店表
    * @return IPage 分页结果
    */
    IPage page(Page page, CourseAuthStoreQuery courseAuthStoreQuery);


    /**
    * 新增课程授权门店表
    * @param courseAuthStoreDTO 课程授权门店表
    * @return boolean 执行结果
    */
    boolean add(CourseAuthStoreDTO courseAuthStoreDTO);


    /**
    * 修改课程授权门店表
    * @param courseAuthStoreDTO 课程授权门店表
    * @return boolean 执行结果
    */
    boolean edit(CourseAuthStoreDTO courseAuthStoreDTO);


    /**
    * 导出excel 课程授权门店表表格
    * @param courseAuthStoreQuery 查询条件
    * @param ids 导出指定ID
    * @return List<CourseAuthStoreVO> 结果集合
    */
    List<CourseAuthStoreVO> export(CourseAuthStoreQuery courseAuthStoreQuery, Long[] ids);


    /**
     *  TODO
     *
     * <AUTHOR>
     * @date 2025年05月22日 10时24分
     */
    IPage pageByStoreId(Page page, CourseAuthStoreQuery courseAuthStoreQuery);

    /**
     * 根据课程类型ID和门店ID查询该门店下所有授权的课程ID列表（包含历史记录）
     *
     * @param courseTypeId 课程类型ID（可选）
     * @param storeId 门店ID
     * @return 课程ID列表
     */
    List<Long> listAuthCourseIds(Long courseTypeId, Long storeId);

    /**
     * 根据课程类型ID和门店ID查询该门店下当前生效的授权课程ID列表
     *
     * @param courseTypeId 课程类型ID（可选）
     * @param storeId 门店ID
     * @return 课程ID列表
     */
    List<Long> listCurrentAuthCourseIds(Long courseTypeId, Long storeId);
}
