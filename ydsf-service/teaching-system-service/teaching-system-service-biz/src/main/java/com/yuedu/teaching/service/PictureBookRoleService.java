package com.yuedu.teaching.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yuedu.teaching.dto.PictureBookRoleDTO;
import com.yuedu.teaching.entity.PictureBookRole;
import com.yuedu.teaching.vo.PictureBookRoleVO;

import java.util.List;

/**
 * 绘本角色表 服务类
 *
 * <AUTHOR>
 * @date 2025-01-03 14:49:28
 */
public interface PictureBookRoleService extends IService<PictureBookRole> {

    /**
     * 添加角色
     *
     * @param pictureBookRole 角色
     * @return Boolean
     */
    Boolean addRole(PictureBookRoleDTO pictureBookRole);

    /**
     * 获取角色列表
     *
     * @param bookId 书籍id
     */
    List<PictureBookRoleVO> getRoles(Integer bookId);

    /**
     * 更新角色列表
     *
     * @param pictureBookRole 角色列表
     */
    Boolean updateRoles(List<PictureBookRoleDTO> pictureBookRole);

    /**
     * 更新单个绘本角色
     *
     * @param pictureBookRole 角色信息
     * @return 更新结果
     */
    Boolean updateRole(PictureBookRoleDTO pictureBookRole);

    /**
     * 批量新增更新角色
     *
     * @param pictureBookRoleList 角色列表
     * @return Boolean
     */
    Boolean batchSaveOrUpdateRoles(List<PictureBookRoleDTO> pictureBookRoleList);
}
