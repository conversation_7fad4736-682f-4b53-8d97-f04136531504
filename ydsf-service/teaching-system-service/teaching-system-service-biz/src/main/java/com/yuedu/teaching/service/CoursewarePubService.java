package com.yuedu.teaching.service;

import com.yuedu.teaching.vo.PictureBookRolePcVO;
import com.yuedu.teaching.vo.StepPubVO;

import java.util.List;


/**
 * 发版课件表 服务类
 *
 * <AUTHOR>
 * @date 2024-11-27 15:01:46
 */
public interface CoursewarePubService {
    /**
     * 根据课件获取 课件环节列表
     * @param coursewareId 课件ID
     * @param dataTemplateId 资料ID
     * @return List<StepPubVO>
     */
    List<StepPubVO> getStepDetails(Integer coursewareId, Integer dataTemplateId,Integer version);
    /**
     * 获取角色列表
     * @param coursewareId 课件ID
     * @return List<PictureBookRolePcVO>
     */
    List<PictureBookRolePcVO> getRoleList(Integer coursewareId);
}
