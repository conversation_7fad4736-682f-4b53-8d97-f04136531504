package com.yuedu.teaching.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yuedu.teaching.dto.CoursewareDataDTO;
import com.yuedu.teaching.entity.CoursewareData;
import com.yuedu.teaching.vo.CoursewareDataVO;
import com.yuedu.teaching.vo.DataTemplateVO;

import java.util.List;
import java.util.Map;


/**
 * 资料表 服务类
 *
 * <AUTHOR>
 * @date 2024-11-06 08:40:10
 */
public interface CoursewareDataService extends IService<CoursewareData> {

    /**
     * 新增资料信息
     *
     * @param coursewareDataDTO 新增资料实体类
     * @return CoursewareDataVO
     */
    CoursewareDataVO insert(CoursewareDataDTO coursewareDataDTO);

    /**
     * 更新资料信息（上传图片）
     *
     * @param coursewareDataDTO 实体类
     * @return CoursewareDataVO
     */
    CoursewareDataVO updateCoursewareData(CoursewareDataDTO coursewareDataDTO);

    /**
     * 查询资料列表
     *
     * @param data coursewareData实体类
     * @return List<CoursewareDataVO>
     */
    Map<String, List<DataTemplateVO>> getCoursewareDataList(CoursewareData data);

    /**
     * 根据主键id设置发布状态
     *
     * @param id     coursewareDataId
     * @param status 0不可发布，1可发布
     * @param coursewareId coursewareId
     */
    void setPubStatusById(Integer id, Integer status,Integer coursewareId);

    /**
     * 根据主键id设置发布状态
     *
     * @param id     coursewareDataId
     * @param status 0不可发布，1可发布
     * @param coursewareId coursewareId
     */
    void setPubCanPublish(Integer id, Integer status,Integer coursewareId);

    /**
     * 根据主键id设置是否开放给门店/设置是否允许下载
     *
     * @param id     coursewareDataId
     * @param status 0开放，1不开放/0允许，1不允许
     *
     */
    void setStoreStatus(CoursewareDataDTO coursewareDataDTO ,Integer type);

}
