package com.yuedu.teaching.controller.web;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yuedu.teaching.constant.TeachingConstant;
import com.yuedu.teaching.constant.enums.CoursewareEnum;
import com.yuedu.teaching.dto.*;
import com.yuedu.teaching.entity.Course;
import com.yuedu.teaching.service.CourseService;
import com.yuedu.teaching.vo.CourseVO;
import com.yuedu.ydsf.common.core.util.R;
import com.yuedu.ydsf.common.security.annotation.StorePermission;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.*;

/**
 * 课程
 * <AUTHOR>
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("web/course")
@Tag(description = "webCourse", name = "指导师端课程")
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
@StorePermission
public class WebCourseController {

    private final CourseService courseService;

    /**
     * 分页查询
     *
     * @param page           分页对象
     * @param courseQueryDTO 查询课程表DTO
     * @return R
     */
    @Operation(summary = "分页查询", description = "分页查询")
    @GetMapping("/page")
    public R<Page<CourseVO>> getCoursePage(@ParameterObject Page<Course> page, @ParameterObject CourseQueryDTO courseQueryDTO) {
        courseQueryDTO.setDisable(TeachingConstant.COURSE_NOT_DISABLE);
        courseQueryDTO.setPublishStatus(CoursewareEnum.IS_PUBLISHED.getCode());
        return R.ok(courseService.listCoursesByStoreId(page, courseQueryDTO));
    }
}
