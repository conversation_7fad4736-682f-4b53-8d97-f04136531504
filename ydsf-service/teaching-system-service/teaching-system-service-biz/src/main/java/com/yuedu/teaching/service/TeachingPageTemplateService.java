package com.yuedu.teaching.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yuedu.teaching.dto.TeachingPageTemplateDTO;
import com.yuedu.teaching.entity.TeachingPageTemplateEntity;
import com.yuedu.teaching.vo.TeachingPageTemplateVO;

import java.util.List;

public interface TeachingPageTemplateService extends IService<TeachingPageTemplateEntity> {
    /**
     * 查询全部教学页
     *
     * @return List<TeachingPageTemplateVO>
     */
    List<TeachingPageTemplateVO> listTemplates(Integer type, Integer category, Integer enabled);

    /**
     * 通过模板id获取教学模板详情
     *
     * @param id 模板id
     * @return 模板详情
     */
    TeachingPageTemplateVO getTeachingPageTemplateById(Integer id);

    /**
     * 新增教学页
     *
     * @param teachingPageTemplate 新增教学DTO
     * @return Boolean
     */
    Boolean saveTemplate(TeachingPageTemplateDTO teachingPageTemplate);

    /**
     * 修改教学页
     *
     * @param teachingPageTemplate 更新教学页DTO
     * @return Boolean
     */
    Boolean updateTemplate(TeachingPageTemplateDTO teachingPageTemplate);


}