package com.yuedu.teaching.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yuedu.teaching.dto.BookVersionDTO;
import com.yuedu.teaching.entity.BookVersionStage;
import com.yuedu.teaching.mapper.BookVersionStageMapper;
import com.yuedu.teaching.service.BookVersionStageService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 书籍版本适配阶段 服务类
 *
 * <AUTHOR>
 * @date 2024-10-24 08:57:30
 */
@Slf4j
@Service
public class BookVersionStageServiceImpl extends ServiceImpl<BookVersionStageMapper, BookVersionStage> implements BookVersionStageService {

    /**
     * 新增关联书籍版本适配阶段
     *
     * @param bookVersionDTO 书籍版本
     */
    @Override
    public void addBookVersionStage(BookVersionDTO bookVersionDTO, Integer bookVersionId) {
        //把阶段id集合拆开存储到对应的书籍版本适配阶段表
        for (Integer stageId : bookVersionDTO.getStageId()) {
            BookVersionStage bookVersionStage = new BookVersionStage();
            BeanUtil.copyProperties(bookVersionDTO, bookVersionStage, "stageId");
            bookVersionStage.setStageId(stageId);
            bookVersionStage.setBookVersionId(bookVersionId);
            this.save(bookVersionStage);
        }
    }

    /**
     * 删除关联书籍版本适配阶段
     *
     * @param bookVersionDTO 书籍版本
     */
    @Override
    public void subtractVersionCount(BookVersionDTO bookVersionDTO) {
        this.remove(Wrappers.lambdaUpdate(BookVersionStage.class)
                .eq(BookVersionStage::getBookVersionId, bookVersionDTO.getBookVersionId()));
    }

    /**
     * 获取书籍对应的每个阶段的数量
     *
     * @param bookId 书籍id
     * @return map 键为书籍阶段id，值为数量
     */
    @Override
    public Map<Integer, Integer> getStageCountByBookId(Integer bookId, Integer bookVersionId) {
        // 创建一个 Map 键为书籍阶段id，值为数量
        Map<Integer, Integer> stageCountMap = new HashMap<>();
        LambdaQueryWrapper<BookVersionStage> queryWrapper = new LambdaQueryWrapper<BookVersionStage>()
                .eq(BookVersionStage::getBookId, bookId)
                .eq(ObjectUtil.isNotEmpty(bookVersionId), BookVersionStage::getBookVersionId, bookVersionId)
                .select(BookVersionStage::getStageId);
        List<BookVersionStage> records = this.list(queryWrapper);
        // 遍历结果，统计每个 stage_id 的数量
        for (BookVersionStage bookVersionStage : records) {
            Integer stageId = bookVersionStage.getStageId();
            stageCountMap.put(stageId, stageCountMap.getOrDefault(stageId, 0) + 1);
        }
        return stageCountMap;
    }

    /**
     * 修改关联书籍版本适配阶段
     *
     * @param bookVersionDTO 书籍版本
     */
    @Override
    public void updateBookVersionStage(BookVersionDTO bookVersionDTO) {
        //输入的阶段idList
        List<Integer> inputStageIdList = new ArrayList<>(bookVersionDTO.getStageId());
        //现存的阶段idList
        Set<Integer> hasStageSet = this.list(Wrappers.lambdaQuery(BookVersionStage.class)
                        .eq(BookVersionStage::getBookVersionId, bookVersionDTO.getBookVersionId()))
                .stream()
                .map(BookVersionStage::getStageId)
                .collect(Collectors.toSet());
        // 要新增的阶段idList
        Set<Integer> toAddStageSet = new HashSet<>(inputStageIdList);
        toAddStageSet.removeAll(hasStageSet);
        // 要删除的阶段idList
        Set<Integer> toDeleteStageSet = new HashSet<>(hasStageSet);
        inputStageIdList.forEach(toDeleteStageSet::remove);
        for (Integer stageId : toAddStageSet) {
            BookVersionStage bookVersionStage = new BookVersionStage();
            BeanUtil.copyProperties(bookVersionDTO, bookVersionStage, "stageId");
            bookVersionStage.setStageId(stageId);
            this.save(bookVersionStage);
        }
        if (CollUtil.isNotEmpty(toDeleteStageSet)) {
            this.remove(Wrappers.lambdaUpdate(BookVersionStage.class)
                    .in(BookVersionStage::getStageId, toDeleteStageSet)
                    .eq(BookVersionStage::getBookVersionId, bookVersionDTO.getBookVersionId())
                    .eq(BookVersionStage::getBookId, bookVersionDTO.getBookId()));
        }
    }
}
