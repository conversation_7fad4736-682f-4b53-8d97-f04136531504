package com.yuedu.teaching.controller.pc;

import com.yuedu.teaching.dto.CourseQueryDTO;
import com.yuedu.teaching.service.CourseService;
import com.yuedu.teaching.vo.CourseVO;
import com.yuedu.ydsf.common.core.util.R;
import com.yuedu.ydsf.common.security.annotation.Inner;
import com.yuedu.ydsf.common.security.annotation.PcPermission;
import com.yuedu.ydsf.common.security.component.PcPermissionAspect;
import com.yuedu.ydsf.common.security.util.PcContextHolder;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * @ClassName PcCourseController
 * @Description PC端课程管理控制类
 * <AUTHOR>
 * @Date 2025/1/6 09:31
 * @Version v0.0.1
 */

@RestController
@RequiredArgsConstructor
@RequestMapping("/pcCourse")
@Tag(description = "pcCourse", name = "PC端课程表管理")
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
@Inner(value = false)
public class PcCourseController {

    private final CourseService courseService;

    /**
     * 获取所有已发布课程列表
     *
     * @return R<List<CourseVO>>
     */
    @Operation(summary = "获取所有已发布课程列表", description = "获取所有已发布课程列表")
    @GetMapping("/pubList")
    @PcPermission
    public R<List<CourseVO>> getCoursePubList(@ParameterObject CourseQueryDTO courseQueryDTO) {
        courseQueryDTO.setStoreId(PcContextHolder.getStoreId());
        return R.ok(courseService.getCourseListByStoreId(courseQueryDTO));
    }
}
