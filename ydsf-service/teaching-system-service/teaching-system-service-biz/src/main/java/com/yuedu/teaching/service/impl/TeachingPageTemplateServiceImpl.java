package com.yuedu.teaching.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yuedu.teaching.dto.TeachingPageTemplateDTO;
import com.yuedu.teaching.entity.TeachingPageTemplateEntity;
import com.yuedu.teaching.mapper.TeachingPageTemplateMapper;
import com.yuedu.teaching.service.TeachingPageTemplateService;
import com.yuedu.teaching.vo.TeachingPageTemplateVO;
import com.yuedu.ydsf.common.core.exception.BizException;
import com.yuedu.ydsf.common.core.util.FileUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.List;


/**
 * 教学页模版表
 *
 * <AUTHOR>
 * @date 2024-11-05 16:39:34
 */
@Service
public class TeachingPageTemplateServiceImpl extends ServiceImpl<TeachingPageTemplateMapper, TeachingPageTemplateEntity> implements TeachingPageTemplateService {

    /**
     * 查询全部信息
     *
     * @return List<TeachingPageTemplateVO>
     */
    @Override
    public List<TeachingPageTemplateVO> listTemplates(Integer type, Integer category, Integer enabled) {
        // 实现查询逻辑
        return baseMapper.selectList(Wrappers.lambdaQuery(TeachingPageTemplateEntity.class)
                .eq(type != 0, TeachingPageTemplateEntity::getType, type)
                .eq(category != 0, TeachingPageTemplateEntity::getCategory, category)
                .eq(enabled != 2, TeachingPageTemplateEntity::getEnabled, enabled)
                .orderByAsc(TeachingPageTemplateEntity::getPageOrder)
        ).stream().map(teachingPageTemplateEntity -> {
            TeachingPageTemplateVO teachingPageTemplateVo = new TeachingPageTemplateVO();
            BeanUtils.copyProperties(teachingPageTemplateEntity, teachingPageTemplateVo);
            teachingPageTemplateVo.setPath(FileUtils.completeUrl(teachingPageTemplateVo.getUrl()));
            return teachingPageTemplateVo;
        }).toList();
    }

    /**
     * 通过模板id获取模板详情
     *
     * @param id 模板id
     * @return 教学页模板详情
     */
    @Override
    public TeachingPageTemplateVO getTeachingPageTemplateById(Integer id) {

        TeachingPageTemplateVO detailsVO = new TeachingPageTemplateVO();
        TeachingPageTemplateEntity entity = getOne(Wrappers.lambdaQuery(TeachingPageTemplateEntity.class)
                .eq(TeachingPageTemplateEntity::getId, id)
        );
        if (ObjectUtil.isEmpty(entity)){
            throw new BizException(" 该模板id没有对应教学模板页");
        }else{
            BeanUtil.copyProperties(entity, detailsVO);
            detailsVO.setPath(FileUtils.completeUrl(entity.getUrl()));
        }
        return detailsVO;
    }

    /**
     * 新增教学页模板
     *
     * @param teachingPageTemplateQueryDTO 新增教学页DTO
     * @return Boolean
     */
    @Override
    public Boolean saveTemplate(TeachingPageTemplateDTO teachingPageTemplateQueryDTO) {
        TeachingPageTemplateEntity teachingPageTemplateEntity = BeanUtil.copyProperties(teachingPageTemplateQueryDTO, TeachingPageTemplateEntity.class);
        // 判断是否为json
        try {
            JSONUtil.parseObj(teachingPageTemplateQueryDTO.getAttr());
        } catch (Exception e) {
            throw new BizException("attr JSON格式有误", e);
        }
        try {
            this.save(teachingPageTemplateEntity);
        } catch (Exception e) {
            log.error("教学页保存失败", e);
            throw new BizException("保存失败");
        }
        return Boolean.TRUE;
    }


    /**
     * 修改教学页模板
     *
     * @param teachingPageTemplateQueryDTO 更新教学页DTO
     * @return Boolean
     */
    @Override
    public Boolean updateTemplate(TeachingPageTemplateDTO teachingPageTemplateQueryDTO) {
        TeachingPageTemplateEntity teachingPageTemplateEntity = BeanUtil.copyProperties(teachingPageTemplateQueryDTO, TeachingPageTemplateEntity.class);
        // 判断是否为json
        try {
            JSONUtil.parseObj(teachingPageTemplateQueryDTO.getAttr());
        } catch (Exception e) {
            throw new BizException("attr JSON格式有误", e);
        }
        this.updateById(teachingPageTemplateEntity);
        return Boolean.TRUE;
    }

}