package com.yuedu.teaching.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yuedu.teaching.dto.*;
import com.yuedu.teaching.entity.LessonEntity;
import com.yuedu.teaching.vo.LessonCoursewareVO;
import com.yuedu.teaching.vo.LessonNameVO;
import com.yuedu.teaching.vo.LessonPracticeVO;
import com.yuedu.teaching.vo.LessonVO;

import java.util.List;

/**
 * @ClassName LessonService
 * @Description 课程章节service
 * <AUTHOR>
 * @Date 2024/10/24 9:48
 * @Version v0.0.1
 */
public interface LessonService extends IService<LessonEntity> {

    /**
     * 根据课程Id查询课程下的课节数量
     *
     * @param courseId 课程Id
     * @return 课节数量
     */
    Long getLessonCount(Long courseId);

    /**
     * 通过Id查询课节信息
     *
     * @param id 课节id
     * @return 课节信息
     */
    LessonVO getLessonById(Integer id);

    /**
     * 通过课程Id获取课节列表
     *
     * @param courseId 课程Id
     * @return 课节列表
     */
    List<LessonVO> getLessonList(Integer courseId);

    /**
     * 通过id列表获取课节名称列表
     *
     * @param ids 课节id列表
     * @return 课节名称列表
     */
    List<LessonNameVO> listLessonNameByIds(List<Long> ids);

    /**
     * 通过Id列表查询课件Id和课件名称
     *
     * @param ids id列表
     * @return List<CoursewareVO>
     */
    List<LessonCoursewareVO> listCoursewareByIds(List<Integer> ids);


    /**
     * 保存课节
     *
     * @param lessonAddDTO 课节信息
     */
    int saveLesson(LessonAddDTO lessonAddDTO);


    /**
     * 修改课节
     *
     * @param updateDTO 课节信息
     */
    int updateLessonById(LessonUpdateDTO updateDTO);


    /**
     * 批量更新课节为发布状态
     *
     * @param canPublishIdList 可发布课节Id列表
     */
    int updateLessonByList(List<Long> canPublishIdList);


    /**
     * 通过id删除课程章节表
     *
     * @return 结果
     */
    boolean removeLesson(RemoveLessonDto removeLessonDto);


    /**
     * 调整课节顺序
     *
     * @param updateBatchLessonDto 列表
     * @return 结果
     */
    int updateBatchByList(UpdateBatchLessonDto updateBatchLessonDto);

    /**
     * 根据课程id获取已发布的课节列表
     *
     * @param courseId 课程id
     * @param version 版本(非必传)
     * @return List<LessonVO>
     */
    List<LessonVO> getPublishLessonList(Long courseId, Integer version);

    /**
     * 根据课节名称查询已发布的课节列表
     *
     * @param lessonName 课节名称
     * @return List<LessonEntity>
     */
    List<LessonEntity> getPublishLessonListByName(Long courseId, String lessonName);

    /**
     * 根据课节id列表查询已发布课节列表
     *
     * @param idList 课节id列表
     * @return List<LessonVO>
     */
    List<LessonVO> getPublishLessonListById(List<Integer> idList);

    /**
     * 根据版本集合获取课节名称集合
     *
     * @param versionList 版本集合
     * @return List<LessonNameVO>
     */
    List<LessonNameVO> getLessonNameList(List<Integer> versionList);

    /**
     * 根据课程id和课节排序集合获取课节列表
     *
     * @param lessonOrderDTOList 课节排序DTO集合
     * @return List<LessonVO>
     */
    List<LessonVO> getLessonListByOrder(List<LessonOrderDTO> lessonOrderDTOList);

    /**
     * 获取课节练课列表
     *
     * @param courseId 课程id
     * @param version 版本
     * @return List<LessonPracticeVO>
     */
    List<LessonPracticeVO> getLessonPracticeList(Long courseId, Integer version);
}