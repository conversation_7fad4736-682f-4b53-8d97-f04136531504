package com.yuedu.teaching.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yuedu.teaching.dto.BookVersionDTO;
import com.yuedu.teaching.entity.BookVersion;
import com.yuedu.teaching.vo.BookVersionVO;

import java.util.List;

/**
 * 书籍版本 服务类
 *
 * <AUTHOR>
 * @date 2024-10-24 08:43:58
 */
public interface BookVersionService extends IService<BookVersion> {
    /**
     * 新增书籍版本系列操作，isbn不存在则新增，isbn存在且已逻辑删除时恢复
     *
     * @param bookVersionDTO 书籍版本
     * @return boolean
     */
    boolean addBookVersionSeries(BookVersionDTO bookVersionDTO);

    /**
     * 删除书籍版本系列操作
     *
     * @param bookVersionDTO 书籍版本
     * @return boolean
     */
    boolean subtractVersionSeries(BookVersionDTO bookVersionDTO);

    /**
     * 修改书籍版本系列操作
     *
     * @param bookVersionDTO 书籍版本
     * @return boolean
     */
    boolean updateVersionSeries(BookVersionDTO bookVersionDTO);

    /**
     * 新增书籍版本
     *
     * @param bookVersionDTO 书籍版本
     */
    Integer addBookVersion(BookVersionDTO bookVersionDTO);

    /**
     * 通过书籍版本id获取书籍版本详情
     *
     * @param id 书籍版本id
     * @return 书籍版本详情
     */
    BookVersionVO getBookVersionById(Integer id);

    /**
     * 还原删除的书籍版本
     *
     * @param bookVersionDTO 书籍版本
     */
    void restoreDeletedVersion(BookVersionDTO bookVersionDTO);

    /**
     * 删除书籍版本
     *
     * @param bookVersionDTO 书籍版本
     */
    void subtractVersionCount(BookVersionDTO bookVersionDTO);

    /**
     * 修改书籍版本
     *
     * @param bookVersionDTO 书籍版本
     */
    void updateBookVersion(BookVersionDTO bookVersionDTO);

    /**
     * 书籍版本列表条件查询
     *
     * @param bookVersion 书籍版本查询条件
     * @return List<BookVersionVO>
     */
    List<BookVersionVO> getBookVersion(BookVersion bookVersion);

    /**
     * 书籍版本分页查询
     *
     * @param page 分页参数
     * @param bookVersion 书籍版本
     * @return Page<BookVersionVO>
     */
    Page<BookVersionVO> selectBookVersionList(Page<BookVersion> page, BookVersion bookVersion);
}
