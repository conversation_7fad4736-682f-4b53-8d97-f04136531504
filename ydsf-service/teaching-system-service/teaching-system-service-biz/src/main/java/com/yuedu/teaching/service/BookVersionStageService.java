package com.yuedu.teaching.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yuedu.teaching.dto.BookVersionDTO;
import com.yuedu.teaching.entity.BookVersionStage;

import java.util.Map;


/**
 * 书籍版本适配阶段 服务类
 *
 * <AUTHOR>
 * @date 2024-10-24 08:57:30
 */
public interface BookVersionStageService extends IService<BookVersionStage> {
    /**
     * 新增关联书籍版本适配阶段
     *
     * @param bookVersionDTO 书籍版本
     */
    void addBookVersionStage(BookVersionDTO bookVersionDTO, Integer bookVersionId);

    /**
     * 删除关联书籍版本适配阶段
     *
     * @param bookVersionDTO 书籍版本
     */
    void subtractVersionCount(BookVersionDTO bookVersionDTO);

    /**
     * 获取书籍对应的每个阶段的数量
     *
     * @param bookId 书籍id
     * @return map 键为书籍阶段id，值为数量
     */
    Map<Integer, Integer> getStageCountByBookId(Integer bookId, Integer bookVersionId);

    /**
     * 修改关联书籍版本适配阶段
     *
     * @param bookVersionDTO 书籍版本
     */
    void updateBookVersionStage(BookVersionDTO bookVersionDTO);
}
