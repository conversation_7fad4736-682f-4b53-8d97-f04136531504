package com.yuedu.teaching.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import com.yuedu.teaching.entity.Courseware;
import com.yuedu.teaching.entity.CoursewareData;
import com.yuedu.teaching.entity.CoursewareDataStepDetails;
import com.yuedu.teaching.entity.CoursewareDataStepDetailsPub;
import com.yuedu.teaching.mapper.CoursewareDataStepDetailsMapper;
import com.yuedu.teaching.mapper.CoursewareDataStepDetailsPubMapper;
import com.yuedu.teaching.mapper.CoursewareMapper;
import com.yuedu.teaching.query.CoursewareDataStepDetailsQuery;
import com.yuedu.teaching.service.CoursewareDataStepDetailsPubService;
import com.yuedu.teaching.vo.CoursewareDataStepDetailsVO;
import com.yuedu.ydsf.common.core.exception.CheckedException;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 资料数据和教学环节详情版本表 服务类
 *
 * <AUTHOR>
 * @date 2024-11-06 08:42:46
 */
@Slf4j
@Service
@AllArgsConstructor
public class CoursewareDataStepDetailsPubServiceImpl extends ServiceImpl<CoursewareDataStepDetailsPubMapper, CoursewareDataStepDetailsPub> implements CoursewareDataStepDetailsPubService {

    private final CoursewareDataStepDetailsMapper courseWareDataStepDetailsMapper;

    private final CoursewareDataStepDetailsPubMapper coursewareDataStepDetailsPubMapper;

    private final CoursewareMapper coursewareMapper;

    /**
     * 发布details表
     *
     * @param data                 coursewareData实体类
     * @param coursewareStepIdList step表id集合
     * @param coursewareDataIdList data表id集合
     */
    @Override
    public void pubCourseWareDataStepDetails(CoursewareData data
            , List<Integer> coursewareStepIdList, List<Integer> coursewareDataIdList, Integer coursewareVersionId) {
        log.info("发布DataStepDetails表:{}", data);
        List<CoursewareDataStepDetails> courseWareDataStepDetailsList = courseWareDataStepDetailsMapper
                .selectList(Wrappers.lambdaQuery(CoursewareDataStepDetails.class)
                        .eq(CoursewareDataStepDetails::getCoursewareId, data.getCoursewareId())
                        .in(CoursewareDataStepDetails::getCoursewareDataId, coursewareDataIdList)
                        .in(CoursewareDataStepDetails::getStepId, coursewareStepIdList));

        List<CoursewareDataStepDetailsPub> coursewareDataStepDetailsPubList = courseWareDataStepDetailsList.stream()
                .map(courseWareDataStepDetails -> {
                    CoursewareDataStepDetailsPub coursewareDataStepDetailsPub = new CoursewareDataStepDetailsPub();
                    BeanUtil.copyProperties(courseWareDataStepDetails, coursewareDataStepDetailsPub);
                    coursewareDataStepDetailsPub.setVersion(coursewareVersionId);
                    coursewareDataStepDetailsPub.setCoursewareDataStepDetailsId(courseWareDataStepDetails.getId());
                    coursewareDataStepDetailsPub.setId(null);
                    return coursewareDataStepDetailsPub;
                })
                .toList();

        coursewareDataStepDetailsPubMapper.insert(coursewareDataStepDetailsPubList);
    }

    /**
     * 获取互动页选项参数
     * @param coursewareDataStepDetailsQuery coursewareId stepId
     * @return R
     */
    @Override
    public CoursewareDataStepDetailsVO queryDetails(CoursewareDataStepDetailsQuery coursewareDataStepDetailsQuery) {
        //看有没有版本，没有先拿版本
        if (ObjectUtil.isEmpty(coursewareDataStepDetailsQuery.getVersion())){
            Courseware courseware = coursewareMapper.selectById(coursewareDataStepDetailsQuery.getCoursewareId());
            if (ObjectUtil.isEmpty(courseware.getVersion())){
                log.warn("版本为空");
                throw new CheckedException("版本为空");
            }
            coursewareDataStepDetailsQuery.setVersion(courseware.getVersion());
            log.info("拿到courseware:{},的版本:{}",courseware.getId(),courseware.getVersion());
        }

        //根据版本，两个id拿details
        CoursewareDataStepDetailsPub coursewareDataStepDetailsPub = coursewareDataStepDetailsPubMapper.
                selectOne(Wrappers.lambdaQuery(CoursewareDataStepDetailsPub.class)
                        .eq(CoursewareDataStepDetailsPub::getVersion, coursewareDataStepDetailsQuery.getVersion())
                        .eq(CoursewareDataStepDetailsPub::getCoursewareId, coursewareDataStepDetailsQuery.getCoursewareId())
                        .eq(CoursewareDataStepDetailsPub::getStepId, coursewareDataStepDetailsQuery.getStepId())
                        .last("limit 1"));

        if (ObjectUtil.isEmpty(coursewareDataStepDetailsPub)){
            return null;
        }

        //解析json
        JSONObject details = JSONUtil.parseObj(coursewareDataStepDetailsPub.getDetails());
        CoursewareDataStepDetailsServiceImpl.addPathField(details);
        CoursewareDataStepDetailsVO detailsVO = new CoursewareDataStepDetailsVO();
        BeanUtil.copyProperties(coursewareDataStepDetailsPub,detailsVO);
        detailsVO.setDetails(details);

        return detailsVO;

    }
}
