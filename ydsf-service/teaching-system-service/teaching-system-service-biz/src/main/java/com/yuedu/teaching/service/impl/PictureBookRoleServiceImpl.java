package com.yuedu.teaching.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yuedu.teaching.dto.PictureBookRoleDTO;
import com.yuedu.teaching.entity.PictureBookRole;
import com.yuedu.teaching.mapper.PictureBookRoleMapper;
import com.yuedu.teaching.service.PictureBookRoleService;
import com.yuedu.teaching.vo.PictureBookRoleVO;
import com.yuedu.ydsf.common.core.exception.CheckedException;
import com.yuedu.ydsf.common.core.util.FileUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 绘本角色表 服务实现类
 *
 * <AUTHOR>
 * @date 2025-01-03 14:49:28
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class PictureBookRoleServiceImpl extends ServiceImpl<PictureBookRoleMapper, PictureBookRole> implements PictureBookRoleService {

    // 每本绘本最大角色数量
    private static final int MAX_ROLE_COUNT = 6;

    private final PictureBookRoleMapper pictureBookRoleMapper;

    /**
     * 新增角色
     *
     * @param pictureBookRole 角色
     * @return Boolean
     */
    @Override
    public Boolean addRole(PictureBookRoleDTO pictureBookRole) {
        // 校验角色数量和名称
        validateRoleCount(pictureBookRole.getBookId());
        validateRoleName(pictureBookRole.getBookId(), pictureBookRole.getRoleName(), null);

        // 插入新角色
        PictureBookRole newRole = BeanUtil.copyProperties(pictureBookRole, PictureBookRole.class);
        return pictureBookRoleMapper.insert(newRole) > 0;
    }

    /**
     * 获取角色列表
     *
     * @param bookId 绘本ID
     * @return 角色列表
     */
    @Override
    public List<PictureBookRoleVO> getRoles(Integer bookId) {
        if (bookId == null) {
            throw new CheckedException("ID不能为空");
        }
        List<PictureBookRole> pictureBookRoles = getBookRoles(bookId);
        return pictureBookRoles.stream()
                .map(this::convertToVO)
                .toList();
    }

    /**
     * 批量新增更新角色
     *
     * @param pictureBookRoleList 角色列表
     * @return Boolean
     */
    @Override
    public Boolean batchSaveOrUpdateRoles(List<PictureBookRoleDTO> pictureBookRoleList) {
        Integer bookId = validateAndGetBookId(pictureBookRoleList);

        // 获取现有角色
        List<PictureBookRole> existingRoles = getBookRoles(bookId);
        Map<Integer, PictureBookRole> existingRoleMap = existingRoles.stream()
                .collect(Collectors.toMap(PictureBookRole::getId, role -> role));

        // 分离新增和更新的角色
        List<PictureBookRoleDTO> newRoles = new ArrayList<>();
        List<PictureBookRoleDTO> updateRoles = new ArrayList<>();

        for (PictureBookRoleDTO roleDTO : pictureBookRoleList) {
            if (roleDTO.getId() == null) {
                newRoles.add(roleDTO);
            } else {
                if (!existingRoleMap.containsKey(roleDTO.getId())) {
                    throw new CheckedException("角色不存在");
                }
                updateRoles.add(roleDTO);
            }
        }

        // 校验角色总数是否超过限制
        if (existingRoles.size() + newRoles.size() > MAX_ROLE_COUNT) {
            throw new CheckedException(String.format("角色总数不能超过%d个", MAX_ROLE_COUNT));
        }

        // 校验角色名称唯一性
        validateBatchRoleNames(pictureBookRoleList, existingRoles, bookId);

        // 批量新增角色
        if (!newRoles.isEmpty()) {
            List<PictureBookRole> insertRoles = BeanUtil.copyToList(newRoles, PictureBookRole.class);
            pictureBookRoleMapper.insert(insertRoles);
        }

        // 批量更新角色
        if (!updateRoles.isEmpty()) {
            List<PictureBookRole> updatedRoles = BeanUtil.copyToList(updateRoles, PictureBookRole.class);
            pictureBookRoleMapper.updateById(updatedRoles);
        }

        return true;
    }

    /**
     * 批量修改角色
     *
     * @param pictureBookRoleList 角色列表
     * @return Boolean
     */
    @Override
    public Boolean updateRoles(List<PictureBookRoleDTO> pictureBookRoleList) {
        Integer bookId = validateAndGetBookId(pictureBookRoleList);

        // 查询并校验已存在角色

        List<PictureBookRole> existingRoles = getBookRoles(bookId);
        if (existingRoles.isEmpty()) {
            throw new CheckedException("角色不存在");
        }

        // 校验角色名称唯一性
        validateBatchRoleNames(pictureBookRoleList, existingRoles, bookId);

        // 批量更新角色
        Map<Integer, PictureBookRole> existingRoleMap = existingRoles.stream()
                .collect(Collectors.toMap(PictureBookRole::getId, role -> role));
        List<PictureBookRole> updatedRoles = pictureBookRoleList.stream()
                .filter(dto -> Objects.equals(dto.getBookId(), bookId) && existingRoleMap.containsKey(dto.getId()))
                .map(s -> BeanUtil.copyProperties(s, PictureBookRole.class))
                .toList();

        if (!updatedRoles.isEmpty()) {
            pictureBookRoleMapper.updateById(updatedRoles);
        }
        return true;
    }

    /**
     * 修改单个角色
     *
     * @param pictureBookRole 角色
     * @return Boolean
     */
    @Override
    public Boolean updateRole(PictureBookRoleDTO pictureBookRole) {
        // 校验角色是否存在
        PictureBookRole existingRole = pictureBookRoleMapper.selectById(pictureBookRole.getId());
        if (existingRole == null || !Objects.equals(existingRole.getBookId(), pictureBookRole.getBookId())) {
            throw new CheckedException("角色不存在");
        }
        // 校验角色名称
        validateRoleName(pictureBookRole.getBookId(), pictureBookRole.getRoleName(), pictureBookRole.getId());

        // 更新角色信息
        PictureBookRole updateRole = BeanUtil.copyProperties(pictureBookRole, PictureBookRole.class);
        return pictureBookRoleMapper.updateById(updateRole) > 0;
    }

    private Integer validateAndGetBookId(List<PictureBookRoleDTO> pictureBookRoleList) {
        if (pictureBookRoleList.isEmpty()) {
            throw new CheckedException("参数不能为空");
        }
        Integer bookId = pictureBookRoleList.get(0).getBookId();
        if (!pictureBookRoleList.stream().allMatch(role -> Objects.equals(role.getBookId(), bookId))) {
            throw new CheckedException("所有角色必须属于同一个绘本");
        }
        return bookId;
    }

    /**
     * 校验角色数量
     *
     * @param bookId 绘本ID
     */
    private void validateRoleCount(Integer bookId) {
        long count = pictureBookRoleMapper.selectCount(Wrappers.<PictureBookRole>lambdaQuery()
                .eq(PictureBookRole::getBookId, bookId));
        if (count >= MAX_ROLE_COUNT) {
            throw new CheckedException(String.format("角色总数不能超过%d个", MAX_ROLE_COUNT));
        }
    }

    /**
     * 校验角色名称
     *
     * @param bookId 绘本ID
     */
    private void validateRoleName(Integer bookId, String roleName, Integer excludeId) {
        boolean nameExists = pictureBookRoleMapper.exists(Wrappers.<PictureBookRole>lambdaQuery()
                .eq(PictureBookRole::getBookId, bookId)
                .eq(PictureBookRole::getRoleName, roleName)
                .ne(excludeId != null, PictureBookRole::getId, excludeId));
        if (nameExists) {
            throw new CheckedException("角色昵称重复，请重新输入");
        }
    }


    /**
     * 获取角色列表
     *
     * @param bookId 绘本ID
     * @return 角色列表
     */
    private List<PictureBookRole> getBookRoles(Integer bookId) {
        return pictureBookRoleMapper.selectList(Wrappers.<PictureBookRole>lambdaQuery()
                .eq(PictureBookRole::getBookId, bookId));
    }

    /**
     * 校验批量角色名称
     *
     * @param updateRoles   角色列表
     * @param existingRoles 已存在角色列表
     * @param bookId        绘本ID
     */
    private void validateBatchRoleNames(List<PictureBookRoleDTO> updateRoles,
                                        List<PictureBookRole> existingRoles,
                                        Integer bookId) {
        Set<String> existingNames = existingRoles.stream()
                .filter(role -> updateRoles.stream()
                        .noneMatch(dto -> Objects.equals(dto.getId(), role.getId())))
                .map(PictureBookRole::getRoleName)
                .collect(Collectors.toSet());

        Set<String> updateNames = new HashSet<>();
        for (PictureBookRoleDTO dto : updateRoles) {
            if (!Objects.equals(dto.getBookId(), bookId)) {
                continue;
            }
            if (!updateNames.add(dto.getRoleName()) || existingNames.contains(dto.getRoleName())) {
                throw new CheckedException("角色名称不能重复");
            }
        }
    }

    /**
     * 转换角色VO
     *
     * @param role 角色实体
     * @return 角色VO
     */
    private PictureBookRoleVO convertToVO(PictureBookRole role) {
        PictureBookRoleVO vo = BeanUtil.copyProperties(role, PictureBookRoleVO.class);
        PictureBookRoleVO.Avatar avatar = new PictureBookRoleVO.Avatar();
        avatar.setRelativePath(role.getUrl());
        avatar.setFullPath(FileUtils.completeUrl(role.getUrl()));
        vo.setAvatar(avatar);
        vo.setUrl(avatar.getFullPath());
        return vo;
    }
}
