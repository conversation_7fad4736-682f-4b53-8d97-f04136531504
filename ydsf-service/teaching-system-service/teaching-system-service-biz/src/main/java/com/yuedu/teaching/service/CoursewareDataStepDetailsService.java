package com.yuedu.teaching.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yuedu.teaching.dto.CoursewareDataStepDetailsDTO;
import com.yuedu.teaching.entity.CoursewareDataStepDetails;
import com.yuedu.teaching.query.CoursewareDataStepDetailsQuery;
import com.yuedu.teaching.vo.CoursewareDataStepDetailsVO;

/**
 * @ClassName CoursewareDataStepDetailsService
 * @Description 资料数据和教学环节详情Service
 * <AUTHOR>
 * @Date 2024/11/04 9:27
 * @Version v0.0.1
 */
public interface CoursewareDataStepDetailsService extends IService<CoursewareDataStepDetails> {

    /**
     * 查询教学页信息
     *
     * @param coursewareDataStepDetailsQuery 查询条件
     * @return 结果
     */
    CoursewareDataStepDetailsVO getDetails(CoursewareDataStepDetailsQuery coursewareDataStepDetailsQuery);


    /**
     * 添加课件环节与教学页信息
     *
     * @param coursewareDataStepDetails coursewareDataStepDetails
     * @return 结果
     */
    Boolean updateDetails(CoursewareDataStepDetailsDTO coursewareDataStepDetails);

}