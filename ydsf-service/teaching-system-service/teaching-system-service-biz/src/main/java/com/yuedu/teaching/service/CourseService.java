package com.yuedu.teaching.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yuedu.teaching.dto.*;
import com.yuedu.teaching.entity.Course;
import com.yuedu.teaching.vo.CampusAuthVO;
import com.yuedu.teaching.vo.CourseVO;

import java.util.List;
import java.util.Map;

/**
 * 课程表 服务类
 *
 * <AUTHOR>
 * @date 2024-10-24 08:53:59
 */
public interface CourseService extends IService<Course> {

    /**
     * 新增课程表
     *
     * @param courseAddDTO 新增课程表DTO
     * @return Boolean
     */
    Boolean saveCourse(CourseAddDTO courseAddDTO);

    /**
     * 修改课程表
     *
     * @param courseUpdateDTO 更新课程表DTO
     * @return Boolean
     */
    Boolean updateCourse(CourseUpdateDTO courseUpdateDTO);


    /**
     * 删除课程表
     *
     * @param courseRemoveDTO 删除课程表DTO
     * @return Boolean
     */
    Boolean deleteCourse(CourseRemoveDTO courseRemoveDTO);

    /**
     * 查询课程表
     *
     * @param page           分页信息
     * @param courseQueryDTO 查询条件，查询课程表DTO
     * @return Page<CourseVO>
     */
    Page<CourseVO> listCourses(Page<?> page, CourseQueryDTO courseQueryDTO);

    /**
     * 根据ID查询
     *
     * @param courseQueryDTO 查询课程表DTO
     * @return CourseVO
     */
    CourseVO getCourse(CourseQueryDTO courseQueryDTO);


    /**
     * 发布课程
     *
     * @param coursePublishDTO 课程发布类
     * @return 结果
     */
    int coursePublish(CoursePublishDTO coursePublishDTO);

    /**
     * 获取已发布最新版本的课程列表
     *
     * @return List<CourseVO>
     */
    List<CourseVO> getCourseList();

    /**
     * 根据课程名称获取已发布课程列表
     *
     * @param courseName 课程名称
     * @return List<CourseVO>
     */
    List<CourseVO> getCourseListByName(String courseName);

    /**
     * 根据id列表获取最新已发布的课程列表
     *
     * @param idList id列表
     * @return 最新已发布的课程列表
     */
    List<CourseVO> getCourseListByIds(List<Integer> idList);

    /**
     * 根据id列表获取课程Map
     * @param courseIdList id列表
     * @return Map<Long, CourseVO>
     */
    Map<Long, CourseVO> getCourseMapByIdList(List<Long> courseIdList);

    /**
     * 设置课程是否停用
     *
     * @param id 课程id
     * @param disable 是否停用(0未停用，1停用)
     * @return Boolean
     */
    Boolean updateCourseDisable(Integer id,Integer disable);

    /**
     * 根据课程版本集合获取课程列表
     *
     * @param versionList 课程版本集合
     * @return List<CourseVO>
     */
    List<CourseVO> getCourseListByVersion(List<Integer> versionList);

    /**
     * 根据课程阶段ID获取相关课程信息
     * <AUTHOR>
     * @date 2025/4/22 13:31
     * @param courseDTO
     * @return java.util.List<com.yuedu.teaching.vo.CourseVO>
     */
    List<CourseVO> getCourseListByStageId(CourseDTO courseDTO);

    /**
     * 获取授权校区
     * <AUTHOR>
     * @date 2025/5/21 17:11
     * @param courseId
     * @return java.util.List<com.yuedu.teaching.vo.CampusAuthVO>
     */
    List<CampusAuthVO> getAuthStore(Long courseId);

    /**
     * 根据课程类型ID获取课程ID列表
     * @param courseTypeId
     * @return
     */
    List<Integer> getCourseIdByType(Long courseTypeId);


    /**
     *  根据门店ID获取门店课程列表
     *
     * <AUTHOR>
     * @date 2025年06月05日 16时13分
     */
    List<CourseVO> getCourseListByStoreId(CourseQueryDTO courseQueryDTO);/**

     * 门店查询课件列表
     *
     * @param page           分页信息
     * @param courseQueryDTO 查询条件，查询课程表DTO
     * @return Page<CourseVO>
     */
    Page<CourseVO> listCoursesByStoreId(Page<Course> page, CourseQueryDTO courseQueryDTO);


}
