package com.yuedu.teaching.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yuedu.teaching.dto.*;
import com.yuedu.teaching.entity.CoursewareStep;
import com.yuedu.teaching.query.CoursewareStepQuery;
import com.yuedu.teaching.vo.*;

import java.util.List;

/**
 * 教学环节表 服务类
 *
 * <AUTHOR>
 * @date 2024-11-05 17:30:36
 */
public interface CoursewareStepService extends IService<CoursewareStep> {
    /**
     * 新增教学环节
     *
     * @param coursewareStepAddDTO 新增参数
     */
    CoursewareStepVO addCoursewareStep(CoursewareStepDTO coursewareStepAddDTO);

    /**
     * 根据模板新增
     * @param coursewareStepByTemplate 教学模版参数
     * @return boolean
     */
    Boolean copyCoursewareStep(CoursewareStepByTemplateDTO coursewareStepByTemplate);

    /**
     * 查询教学环节
     *
     * @param coursewareStepQuery 查询参数
     * @return 查询结果
     */
    List<StepVO> listCoursewareStep(CoursewareStepQuery coursewareStepQuery);

    /**
     * 根据id查询教学环节
     *
     * @param coursewareStepQuery 查询参数
     * @return 查询结果
     */
    CoursewareStepVO getCoursewareStepById(CoursewareStepQuery coursewareStepQuery);

    /**
     * 调整教学环节顺序
     *
     * @param coursewareStepOrderDTO 教学环节更新DTO
     */
    void updateBatchByList(CoursewareStepOrderDTO coursewareStepOrderDTO);

    /**
     * 修改教学环节
     *
     * @param coursewareStepDTO 修改参数
     * @return 修改结果
     */
    CoursewareStepVO updateCoursewareStep(CoursewareStepDTO coursewareStepDTO);

    /**
     * 删除教学环节
     *
     * @param coursewareStepRemoveDTO 删除参数
     * @return 删除结果
     */
    Boolean deleteCoursewareStep(CoursewareStepDTO coursewareStepRemoveDTO);

    /**
     * 预览教学环节
     *
     * @param coursewareStepQuery 查询参数
     * @return 查询结果
     */
    List<ClientStepDetailsVO> viewCoursewareStep(CoursewareStepQuery coursewareStepQuery);
}
