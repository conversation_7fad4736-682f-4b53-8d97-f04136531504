package com.yuedu.teaching.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yuedu.teaching.dto.CoursewareStepTemplateAddDTO;
import com.yuedu.teaching.entity.CoursewareStep;
import com.yuedu.teaching.entity.CoursewareStepTemplate;
import com.yuedu.teaching.mapper.CoursewareStepTemplateMapper;
import com.yuedu.teaching.service.CoursewareStepTemplateService;
import com.yuedu.teaching.vo.CoursewareStepTemplateVO;
import com.yuedu.teaching.vo.StepVO;
import com.yuedu.ydsf.common.core.exception.BizException;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 教学环节模版表 服务类
 *
 * <AUTHOR>
 * @date 2024-11-05 17:32:03
 */
@Slf4j
@Service
@AllArgsConstructor
public class CoursewareStepTemplateServiceImpl extends ServiceImpl<CoursewareStepTemplateMapper, CoursewareStepTemplate> implements CoursewareStepTemplateService {

    private final CoursewareStepServiceImpl coursewareStepService;

    /**
     * 新增教学环节模板
     *
     * @param addDTO 新增参数
     * @return 新增结果
     */
    @Override
    public Boolean addCoursewareStepTemplate(CoursewareStepTemplateAddDTO addDTO) {
        CoursewareStepTemplate template = BeanUtil.copyProperties(addDTO, CoursewareStepTemplate.class);

        // 获取教学环节数据
        List<CoursewareStep> steps = coursewareStepService.getCoursewareStepList(addDTO.getCoursewareId(), addDTO.getCoursewareDataId());

        if (steps.isEmpty()) {
            throw new BizException("新增教学模板环节数据为空");
        }

        template.setDetails(JSONUtil.toJsonStr(coursewareStepService.findTree(steps)));
        return this.save(template);
    }


    /**
     * 查询所有教学环节模板表
     *
     * @return 查询结果
     */
    @Override
    public List<CoursewareStepTemplateVO> listCoursewareStepTemplate() {
        List<CoursewareStepTemplate> templateList = this.list(Wrappers.emptyWrapper());
        return templateList.stream().map(coursewareStepTemplate -> BeanUtil.copyProperties(coursewareStepTemplate, CoursewareStepTemplateVO.class)).toList();
    }

    /**
     * 根据id查询教学环节模板表
     *
     * @param templateId 查询参数
     * @return 查询结果
     */
    @Override
    public List<StepVO> getCoursewareStepByIdTemplate(Integer templateId) {
        CoursewareStepTemplate coursewareStepTemplate = this.getById(templateId);
        if (ObjectUtils.isEmpty(coursewareStepTemplate)) {
            throw new BizException("课程环节模板不存在");
        }
        return JSONUtil.toList(coursewareStepTemplate.getDetails(), StepVO.class);
    }
}
