package com.yuedu.teaching.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yuedu.teaching.dto.BookNameDTO;
import com.yuedu.teaching.dto.BookNameQueryDTO;
import com.yuedu.teaching.dto.BookVersionDTO;
import com.yuedu.teaching.entity.BookName;

import java.util.List;

/**
 * 书名表 服务类
 *
 * <AUTHOR>
 * @date 2024-10-24 08:57:33
 */
public interface BookNameService extends IService<BookName> {


    /**
     * 新增书名
     *
     * @param bookNameDTO 书名请求DTO
     * @return boolean
     */
    BookName insert(BookNameDTO bookNameDTO);

    /**
     * 根据id删除关联关系
     *
     * @param bookNameDTO 书名请求实体类
     * @return 是否删除成功
     */
    Boolean removeBook(BookNameDTO bookNameDTO);

    /**
     * 分页查询
     *
     * @param pageSize         分页大小
     * @param bookNameQueryDTO 书名请求DTO
     * @return 分页结果
     */
    Page<BookName> pageQuery(Page<BookName> pageSize, BookNameQueryDTO bookNameQueryDTO);

    /**
     * 根据书名查询
     *
     * @param bookNameQueryDTO 书名请求实体类
     * @return 书名列表
     */
    List<BookName> queryByName(BookNameQueryDTO bookNameQueryDTO);

    /**
     * 更新书名信息
     *
     * @param bookName 书名
     * @return boolean
     */
    Boolean updateBook(BookName bookName);

    /**
     * 增加或删除书籍版本时，改变书籍版本数
     *
     * @param bookVersionDTO 书籍版本请求DTO
     * @param type           1增加,-1减少
     */
    void addOrSubBookCount(BookVersionDTO bookVersionDTO, Integer type);

    /**
     * 增加课件时，改变课件数量
     *
     * @param bookId 书籍id
     */
    void addCoursewareCount(Integer bookId);
}
