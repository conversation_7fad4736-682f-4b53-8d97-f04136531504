package com.yuedu.teaching.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yuedu.teaching.dto.CourseFeeDTO;
import com.yuedu.teaching.dto.TimetableCourseFeeDTO;
import com.yuedu.teaching.entity.CourseFee;
import com.yuedu.teaching.query.CourseFeeQuery;
import com.yuedu.teaching.query.TimetableCourseFeeQuery;
import com.yuedu.teaching.vo.CourseFeeVO;
import com.yuedu.ydsf.eduConnect.api.dto.TimetableDTO;
import org.apache.commons.lang3.tuple.Pair;

import java.io.Serializable;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;

/**
* 服务接口
*
* <AUTHOR>
* @date  2025/05/22
*/
public interface CourseFeeService extends IService<CourseFee> {



    /**
     * 分页查询
     *
     * @param page 分页对象
     * @param courseFeeQuery 
     * @return IPage 分页结果
     */
    IPage<CourseFeeVO> page(Page page, CourseFeeQuery courseFeeQuery);


    /**
     * 根据ID获得信息
     *
     * @param id id
     * @return CourseFeeVO 详细信息
     */
    CourseFeeVO getInfoById(Serializable id);


    /**
     * 新增
     *
     * @param courseFeeDTO 
     * @return boolean 执行结果
     */
    boolean add(CourseFeeDTO courseFeeDTO);


    /**
     * 修改
     *
     * @param courseFeeDTO 
     * @return boolean 执行结果
     */
    boolean edit(CourseFeeDTO courseFeeDTO);


    /**
     * 导出excel 表格
     *
     * @param courseFeeQuery 查询条件
     * @param ids 导出指定ID
     * @return List<CourseFeeVO> 结果集合
     */
    List<CourseFeeVO> export(CourseFeeQuery courseFeeQuery, Long[] ids);


    /**
     *  根据门店修改课时费
     *
     * <AUTHOR>
     * @date 2025年05月22日 13时49分
     */
    void updateFeeByStoreId(CourseFeeDTO courseFeeDTO,boolean isCustomize);


    /**
     *  根据校区查询当前执行列表
     *
     * <AUTHOR>
     * @date 2025年05月27日 14时46分
     */
    List<CourseFeeVO> getCourseFeeListByStore(Long storeId, Long courseId);


    /**
     *  获得当前执行信息
     *
     * <AUTHOR>
     * @date 2025年05月27日 14时53分
     */
    CourseFeeVO getCurrentCourseFeeByStore(Long storeId);

    /**
     * 查询课次费列表
     * @param timetableCourseFeeQuery 课表列表
     * @return List<CourseFeeDTO> 课次费列表
     */
    Map<Long, CourseFeeDTO> getCourseFeeListByTimetable(TimetableCourseFeeQuery timetableCourseFeeQuery);

    /**
     * 查询课次费列表
     * @param timetableCourseFeeQueryList 课表列表
     * @return List<CourseFeeDTO> 课次费列表
     */
    Map<Long, Map<Long, CourseFeeDTO>> listCourseFeeListByTimetable(List<TimetableCourseFeeQuery> timetableCourseFeeQueryList);
}
