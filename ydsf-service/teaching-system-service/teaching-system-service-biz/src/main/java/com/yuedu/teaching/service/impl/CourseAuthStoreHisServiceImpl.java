package com.yuedu.teaching.service.impl;


import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import org.springframework.beans.BeanUtils;
import cn.hutool.core.util.ArrayUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yuedu.teaching.mapper.CourseAuthStoreHisMapper;
import com.yuedu.teaching.service.CourseAuthStoreHisService;
import com.yuedu.teaching.query.CourseAuthStoreHisQuery;
import com.yuedu.teaching.dto.CourseAuthStoreHisDTO;
import com.yuedu.teaching.vo.CourseAuthStoreHisVO;
import com.yuedu.teaching.entity.CourseAuthStoreHis;

import java.util.List;


/**
* 课程授权门店历史表服务层
*
* <AUTHOR>
* @date  2025/05/21
*/
@Service
public class CourseAuthStoreHisServiceImpl extends ServiceImpl<CourseAuthStoreHisMapper,CourseAuthStoreHis>
    implements CourseAuthStoreHisService{


    /**
    * 课程授权门店历史表分页查询
    * @param page 分页对象
    * @param courseAuthStoreHisQuery 课程授权门店历史表
    * @return IPage 分页结果
    */
    @Override
    public IPage page(Page page,CourseAuthStoreHisQuery courseAuthStoreHisQuery) {
        return page(page, Wrappers.<CourseAuthStoreHis>lambdaQuery());
    }

    /**
    * 新增课程授权门店历史表
    * @param courseAuthStoreHisDTO 课程授权门店历史表
    * @return boolean 执行结果
    */
    @Override
    public boolean add(CourseAuthStoreHisDTO courseAuthStoreHisDTO) {
        CourseAuthStoreHis courseAuthStoreHis = new CourseAuthStoreHis();
        BeanUtils.copyProperties(courseAuthStoreHisDTO, courseAuthStoreHis);
        return save(courseAuthStoreHis);
    }


    /**
    * 修改课程授权门店历史表
    * @param courseAuthStoreHisDTO 课程授权门店历史表
    * @return boolean 执行结果
    */
    @Override
    public boolean edit(CourseAuthStoreHisDTO courseAuthStoreHisDTO) {
        CourseAuthStoreHis courseAuthStoreHis = new CourseAuthStoreHis();
        BeanUtils.copyProperties(courseAuthStoreHisDTO, courseAuthStoreHis);
        return updateById(courseAuthStoreHis);
    }



    /**
    * 导出excel 课程授权门店历史表表格
    * @param courseAuthStoreHisQuery 查询条件
    * @param ids 导出指定ID
    * @return List<CourseAuthStoreHisVO> 结果集合
    */
    @Override
    public List<CourseAuthStoreHisVO> export(CourseAuthStoreHisQuery courseAuthStoreHisQuery, Long[] ids) {
        return list(Wrappers.<CourseAuthStoreHis>lambdaQuery().in(ArrayUtil.isNotEmpty(ids), CourseAuthStoreHis::getId, ids))
            .stream()
            .map(entity -> {
                CourseAuthStoreHisVO courseAuthStoreHisVO = new CourseAuthStoreHisVO();
                BeanUtils.copyProperties(entity, courseAuthStoreHisVO);
                return courseAuthStoreHisVO;
            }).toList();
    }

}
