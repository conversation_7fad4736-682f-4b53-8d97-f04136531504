package com.yuedu.teaching.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yuedu.teaching.dto.CoursewareDTO;
import com.yuedu.teaching.entity.CoursewareData;
import com.yuedu.teaching.entity.CoursewareDataPub;
import com.yuedu.teaching.vo.CoursewareVO;
import com.yuedu.teaching.vo.DataTemplateVO;

import java.util.List;
import java.util.Map;

/**
 * 发布资料表 服务类
 *
 * <AUTHOR>
 * @date 2024-11-06 08:41:10
 */
public interface CoursewareDataPubService extends IService<CoursewareDataPub> {

    /**
     * 发布资料
     *
     * @param data coursewareData实体类
     * @return 是否发布成功
     */
    CoursewareVO publish(CoursewareData data);

    /**
     * 更新课件是否生成录课状态
     * <AUTHOR>
     * @date 2025/3/12 14:13
     * @param courseware
     * @return void
     */
    void updateRecordTaskStatus(CoursewareDTO courseware);


    /**
     * 指导师web端
     * 查询资料列表
     *
     * @param data coursewareData实体类
     * @return List<CoursewareDataVO>
     */
    Map<String, List<DataTemplateVO>> getWebCoursewareDataList(CoursewareData data);
}
