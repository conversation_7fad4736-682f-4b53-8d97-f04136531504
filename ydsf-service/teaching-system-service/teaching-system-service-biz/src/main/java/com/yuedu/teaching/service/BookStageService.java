package com.yuedu.teaching.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yuedu.teaching.dto.BookVersionDTO;
import com.yuedu.teaching.entity.BookStage;

import java.util.Map;

/**
 * <AUTHOR>
 */
public interface BookStageService extends IService<BookStage> {
    /**
     * 新增书籍阶段和书单关联关系
     *
     * @param bookVersionDTO 书籍版本DTO
     * @param bookStageCount 书籍id对应版本数量
     */
    void addBookStage(Map<Integer, Integer> bookStageCount, BookVersionDTO bookVersionDTO);

    /**
     * 删除书籍阶段和书单关联关系
     *
     * @param bookStageCount    书籍对应版本数量
     * @param bookId            书籍ID
     * @param bookStageVerCount 书籍版本对应版本数量
     */
    void subtractBookStage(Map<Integer, Integer> bookStageCount, Map<Integer, Integer> bookStageVerCount, Integer bookId);

    /**
     * 修改书籍阶段和书单关联关系
     *
     * @param bookVersionDTO    书籍版本DTO
     * @param bookStageCount    书籍id对应版本数量
     * @param bookStageVerCount 书籍版本id对应版本数量
     */
    void updateBookStage(Map<Integer, Integer> bookStageCount, Map<Integer, Integer> bookStageVerCount, BookVersionDTO bookVersionDTO);
}