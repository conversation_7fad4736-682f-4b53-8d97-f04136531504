package com.yuedu.teaching.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yuedu.teaching.dto.CoursewareDTO;
import com.yuedu.teaching.entity.Courseware;
import com.yuedu.teaching.vo.CoursewareInfoVO;
import com.yuedu.teaching.vo.CoursewareVO;
import com.yuedu.teaching.vo.CoursewareVersionVO;

import java.util.List;

/**
 * 课件表 服务类
 *
 * <AUTHOR>
 * @date 2024-11-05 15:05:46
 */
public interface CoursewareService extends IService<Courseware> {
    /**
     * 新增课件系列操作
     *
     * @param coursewareDTO 课件DTO
     * @return boolean
     */
    boolean addCoursewareSeries(CoursewareDTO coursewareDTO);

    /**
     * 新增课件
     *
     * @param coursewareDTO 课件DTO
     */
    void addCourseware(CoursewareDTO coursewareDTO);

    /**
     * 课件列表查询
     *
     * @param bookId         书籍id
     * @param coursewareName 课件名称
     * @return List<Courseware>
     */
    List<CoursewareVO> getCoursewareList(Integer bookId, String coursewareName);

    /**
     * 根据书籍id获取课件列表
     *
     * @param bookId 书籍id
     * @return List<CoursewareVO>
     */
    List<CoursewareVO> getCoursewareListByBookId(Integer bookId);

    /**
     * 根据课件id修改课件名称
     *
     * @param id 课件id
     * @param coursewareName 课件名称
     * @return boolean
     */
    boolean updateCourseware(Integer id,String coursewareName);

    /**
     * 判断课件名是否存在
     *
     * @param coursewareName 课件名称
     * @return boolean
     */
    boolean isCoursewareNameExist(String coursewareName);

    /**
     * 判断课件是否存在(加上id判断),根据课件名查询出的课件id和输入id相同则返回true，否则返回false
     *
     * @param id 课件id
     * @param coursewareName 课件名
     * @return boolean
     */
    boolean isCoursewareNameUniqueOrExisting(Integer id,String coursewareName);

    /**
     * 根据版本列表获取课件列表
     *
     * @param versionList 版本列表
     * @return List<CoursewareVersionVO>
     */
    List<CoursewareVersionVO> getCoursewareVersionList(List<Integer> versionList,List<Long> coursewareIdList);

    /**
     * 根据课件id列表获取课件信息及对应书名列表
     *
     * @param idList 课件id列表
     * @return List<CoursewareInfoVO>
     */
    List<CoursewareInfoVO> getCoursewareInfoList(List<Integer> idList);
}
