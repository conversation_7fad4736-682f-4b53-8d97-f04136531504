package com.yuedu.teaching.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yuedu.teaching.dto.CourseTypeDTO;
import com.yuedu.teaching.entity.CourseType;
import com.yuedu.teaching.query.CourseTypeQuery;
import com.yuedu.teaching.vo.CourseTypeVO;
import org.springdoc.core.annotations.ParameterObject;

import java.util.List;

/**
 * 课程类型 服务类
 *
 * <AUTHOR>
 * @date 2025-05-21 08:57:32
 */
public interface CourseTypeService extends IService<CourseType> {

    Page<CourseTypeVO> getCourseTypePage(Page<CourseType> page, CourseTypeQuery courseType);

    /**
     * 根据门店ID查询该门店授权过的课程类型列表
     *
     * @param storeId 门店ID
     * @return 课程类型DTO列表
     */
    List<CourseTypeVO> getCourseTypesByStoreAuth(Long storeId);

}
