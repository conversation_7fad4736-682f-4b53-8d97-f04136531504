package com.yuedu.teaching.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yuedu.teaching.entity.StageEntity;
import com.yuedu.teaching.vo.StageVO;

import java.util.List;

public interface StageService extends IService<StageEntity> {


    /**
     * 查询全部书单信息
     *
     * @return List<StageVO>
     */
    List<StageVO> listBooks(Integer productId);

    /**
     * 分页查询
     *
     * @param page 分页条件
     * @param
     * @return page
     */
    Page<StageEntity> getPage(Page page, Integer productId, String stageName);
}