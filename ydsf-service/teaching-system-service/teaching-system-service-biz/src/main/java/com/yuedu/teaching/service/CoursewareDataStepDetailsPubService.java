package com.yuedu.teaching.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yuedu.teaching.entity.CoursewareData;
import com.yuedu.teaching.entity.CoursewareDataStepDetailsPub;
import com.yuedu.teaching.query.CoursewareDataStepDetailsQuery;
import com.yuedu.teaching.vo.CoursewareDataStepDetailsVO;

import java.util.List;

/**
 * 资料数据和教学环节详情版本表 服务类
 *
 * <AUTHOR>
 * @date 2024-11-06 08:42:46
 */
public interface CoursewareDataStepDetailsPubService extends IService<CoursewareDataStepDetailsPub> {

    /**
     * 发布details表
     *
     * @param data                 coursewareData实体类
     * @param coursewareStepIdList step表id集合
     * @param coursewareDataIdList data表id集合
     */
    void pubCourseWareDataStepDetails(CoursewareData data, List<Integer> coursewareStepIdList, List<Integer> coursewareDataIdList, Integer coursewareVersionId);

    /**
     * 获取互动页选项参数
     * @param coursewareDataStepDetailsQuery coursewareId stepId
     * @return R
     */
    CoursewareDataStepDetailsVO queryDetails(CoursewareDataStepDetailsQuery coursewareDataStepDetailsQuery);
}
